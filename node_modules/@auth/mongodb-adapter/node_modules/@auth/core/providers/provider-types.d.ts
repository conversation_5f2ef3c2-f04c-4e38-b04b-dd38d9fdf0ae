export type OAuthProviderId = "42-school" | "apple" | "asgardeo" | "atlassian" | "auth0" | "authentik" | "azure-ad-b2c" | "azure-ad" | "azure-devops" | "bankid-no" | "battlenet" | "beyondidentity" | "bitbucket" | "box" | "boxyhq-saml" | "bungie" | "click-up" | "cognito" | "coinbase" | "concept2" | "descope" | "discord" | "dribbble" | "dropbox" | "duende-identity-server6" | "eventbrite" | "eveonline" | "facebook" | "faceit" | "figma" | "foursquare" | "freshbooks" | "frontegg" | "fusionauth" | "github" | "gitlab" | "google" | "hubspot" | "huggingface" | "identity-server4" | "instagram" | "kakao" | "keycloak" | "kinde" | "line" | "linkedin" | "logto" | "loops" | "mailchimp" | "mailru" | "mastodon" | "mattermost" | "medium" | "microsoft-entra-id" | "naver" | "netlify" | "netsuite" | "nextcloud" | "notion" | "okta" | "onelogin" | "ory-hydra" | "osso" | "osu" | "passage" | "patreon" | "ping-id" | "pinterest" | "pipedrive" | "reddit" | "roblox" | "salesforce" | "simplelogin" | "slack" | "spotify" | "strava" | "threads" | "tiktok" | "todoist" | "trakt" | "twitch" | "twitter" | "united-effects" | "vipps" | "vk" | "webex" | "wechat" | "wikimedia" | "wordpress" | "workos" | "yandex" | "zitadel" | "zoho" | "zoom";
export type EmailProviderId = "email" | "forwardemail" | "mailgun" | "nodemailer" | "passkey" | "postmark" | "resend" | "sendgrid";
//# sourceMappingURL=provider-types.d.ts.map
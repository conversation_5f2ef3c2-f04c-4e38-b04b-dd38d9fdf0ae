{"version": 3, "file": "adapters.d.ts", "sourceRoot": "", "sources": ["src/adapters.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAA;AACnD,OAAO,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,YAAY,CAAA;AAKzE;;;;;GAKG;AACH,MAAM,WAAW,WAAY,SAAQ,IAAI;IACvC,wCAAwC;IACxC,EAAE,EAAE,MAAM,CAAA;IACV,gCAAgC;IAChC,KAAK,EAAE,MAAM,CAAA;IACb;;;OAGG;IACH,aAAa,EAAE,IAAI,GAAG,IAAI,CAAA;CAC3B;AAED;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,OAAO,CACtC,YAAY,EACZ,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,UAAU,CACxC,CAAA;AAED;;;;;;;;GAQG;AACH,MAAM,WAAW,cAAe,SAAQ,OAAO;IAC7C,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,kBAAkB,CAAA;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B;;;;OAIG;IACH,YAAY,EAAE,MAAM,CAAA;IACpB,4DAA4D;IAC5D,MAAM,EAAE,MAAM,CAAA;IACd;;;;;;;;;;OAUG;IACH,OAAO,EAAE,IAAI,CAAA;CACd;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,iBAAiB;IAChC,gCAAgC;IAChC,UAAU,EAAE,MAAM,CAAA;IAClB,gDAAgD;IAChD,OAAO,EAAE,IAAI,CAAA;IACb;;OAEG;IACH,KAAK,EAAE,MAAM,CAAA;CACd;AAED;;GAEG;AACH,MAAM,WAAW,oBAAqB,SAAQ,aAAa;IACzD;;OAEG;IACH,MAAM,EAAE,MAAM,CAAA;CACf;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,WAAW,OAAO;IACtB;;;;OAIG;IACH,UAAU,CAAC,CAAC,IAAI,EAAE,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,CAAA;IACtD;;;;OAIG;IACH,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,GAAG,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,CAAA;IACnD;;;;OAIG;IACH,cAAc,CAAC,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,CAAA;IAC7D;;;;OAIG;IACH,gBAAgB,CAAC,CACf,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,UAAU,GAAG,mBAAmB,CAAC,GACxE,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,CAAA;IAChC;;;;OAIG;IACH,UAAU,CAAC,CACT,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GACnD,SAAS,CAAC,WAAW,CAAC,CAAA;IACzB;;;;OAIG;IACH,UAAU,CAAC,CACT,MAAM,EAAE,MAAM,GACb,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IAC5D;;;;;OAKG;IACH,WAAW,CAAC,CACV,OAAO,EAAE,cAAc,GACtB,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IAC/D,sDAAsD;IACtD,aAAa,CAAC,CACZ,iBAAiB,EAAE,IAAI,CAAC,cAAc,EAAE,UAAU,GAAG,mBAAmB,CAAC,GACxE,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,CAAA;IACxD;;;;OAIG;IACH,aAAa,CAAC,CAAC,OAAO,EAAE;QACtB,YAAY,EAAE,MAAM,CAAA;QACpB,MAAM,EAAE,MAAM,CAAA;QACd,OAAO,EAAE,IAAI,CAAA;KACd,GAAG,SAAS,CAAC,cAAc,CAAC,CAAA;IAC7B;;;;;;;;OAQG;IACH,iBAAiB,CAAC,CAChB,YAAY,EAAE,MAAM,GACnB,SAAS,CAAC;QAAE,OAAO,EAAE,cAAc,CAAC;QAAC,IAAI,EAAE,WAAW,CAAA;KAAE,GAAG,IAAI,CAAC,CAAA;IACnE;;;;OAIG;IACH,aAAa,CAAC,CACZ,OAAO,EAAE,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,cAAc,CAAC,GACtE,SAAS,CAAC,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IAC/C;;;;;OAKG;IACH,aAAa,CAAC,CACZ,YAAY,EAAE,MAAM,GACnB,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IAC/D;;;;OAIG;IACH,uBAAuB,CAAC,CACtB,iBAAiB,EAAE,iBAAiB,GACnC,SAAS,CAAC,iBAAiB,GAAG,IAAI,GAAG,SAAS,CAAC,CAAA;IAClD;;;;;OAKG;IACH,oBAAoB,CAAC,CAAC,MAAM,EAAE;QAC5B,UAAU,EAAE,MAAM,CAAA;QAClB,KAAK,EAAE,MAAM,CAAA;KACd,GAAG,SAAS,CAAC,iBAAiB,GAAG,IAAI,CAAC,CAAA;IACvC;;;;OAIG;IACH,UAAU,CAAC,CACT,iBAAiB,EAAE,cAAc,CAAC,mBAAmB,CAAC,EACtD,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,GACnC,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,CAAA;IACnC;;;;OAIG;IACH,gBAAgB,CAAC,CACf,YAAY,EAAE,oBAAoB,CAAC,cAAc,CAAC,GACjD,SAAS,CAAC,oBAAoB,GAAG,IAAI,CAAC,CAAA;IACzC;;;;OAIG;IACH,mBAAmB,CAAC,CAClB,aAAa,EAAE,oBAAoB,GAClC,SAAS,CAAC,oBAAoB,CAAC,CAAA;IAClC;;;;;OAKG;IACH,0BAA0B,CAAC,CACzB,MAAM,EAAE,oBAAoB,CAAC,QAAQ,CAAC,GACrC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAA;IACpC;;;;OAIG;IACH,0BAA0B,CAAC,CACzB,YAAY,EAAE,oBAAoB,CAAC,cAAc,CAAC,EAClD,UAAU,EAAE,oBAAoB,CAAC,SAAS,CAAC,GAC1C,SAAS,CAAC,oBAAoB,CAAC,CAAA;CACnC;AAMD,4DAA4D;AAC5D,wBAAgB,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,IAAI,MAAM,CAMtD;AAGD,OAAO,QAAQ,oBAAoB,CAAC;IAClC,KAAK,UAAU,GAAG;SACf,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,SAAS;KAC5B,CAAA;IACD,KAAK,SAAS,GAAG,SAAS,EAAE,CAAA;IAC5B,KAAK,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAA;IACrD,KAAK,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,SAAS,CAAA;IACvD,UAAU,cAAc;QACtB,IAAI,EAAE,OAAO,GAAG,OAAO,GAAG,MAAM,CAAA;QAChC,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,GAAG,SAAS,CAAA;KACrC;CACF"}
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envFile = fs.readFileSync(envPath, 'utf8');
    
    envFile.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  } catch (error) {
    console.error('Could not load .env.local file:', error.message);
  }
}

loadEnvFile();

// User Schema (simplified version for script)
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { 
    type: String, 
    enum: ['admin', 'trainer', 'member'], 
    default: 'member' 
  },
  gymId: { type: mongoose.Schema.Types.ObjectId, ref: 'Gym' },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

// Gym Schema (simplified version for script)
const gymSchema = new mongoose.Schema({
  name: { type: String, required: true },
  subdomain: { type: String, required: true, unique: true },
  domain: { type: String },
  isActive: { type: Boolean, default: true }
});

const Gym = mongoose.model('Gym', gymSchema);

async function testLogin() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Test credentials
    const testCredentials = [
      { email: '<EMAIL>', password: 'password123', expectedGym: 'default' },
      { email: '<EMAIL>', password: 'password123', expectedGym: 'testgym' }
    ];

    for (const cred of testCredentials) {
      console.log(`\n🧪 Testing login for: ${cred.email}`);
      
      // Find user
      const user = await User.findOne({ email: cred.email });
      if (!user) {
        console.log(`❌ User not found: ${cred.email}`);
        continue;
      }

      // Check password
      const isValidPassword = await bcrypt.compare(cred.password, user.password);
      if (!isValidPassword) {
        console.log(`❌ Invalid password for: ${cred.email}`);
        continue;
      }

      // Find gym
      let gym;
      if (user.gymId) {
        gym = await Gym.findById(user.gymId);
      } else {
        gym = await Gym.findOne({ subdomain: 'default' });
      }

      if (!gym) {
        console.log(`❌ No gym found for user: ${cred.email}`);
        continue;
      }

      console.log(`✅ Login successful for: ${cred.email}`);
      console.log(`   - User ID: ${user._id}`);
      console.log(`   - User Role: ${user.role}`);
      console.log(`   - Gym: ${gym.name} (${gym.subdomain})`);
      console.log(`   - Gym ID: ${gym._id}`);
      console.log(`   - User's gymId: ${user.gymId || 'null'}`);
    }

    // List all users and gyms
    console.log('\n📊 Database Summary:');
    const allUsers = await User.find({});
    const allGyms = await Gym.find({});
    
    console.log(`\nUsers (${allUsers.length}):`);
    allUsers.forEach(user => {
      console.log(`  - ${user.email} (${user.role}) -> gymId: ${user.gymId || 'null'}`);
    });
    
    console.log(`\nGyms (${allGyms.length}):`);
    allGyms.forEach(gym => {
      console.log(`  - ${gym.name} (${gym.subdomain}) -> ID: ${gym._id}`);
    });

  } catch (error) {
    console.error('Error testing login:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

testLogin();

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envFile = fs.readFileSync(envPath, 'utf8');

    envFile.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  } catch (error) {
    console.error('Could not load .env.local file:', error.message);
  }
}

loadEnvFile();

// User Schema (simplified version for script)
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { 
    type: String, 
    enum: ['admin', 'trainer', 'member'], 
    default: 'member' 
  },
  gymId: { type: mongoose.Schema.Types.ObjectId, ref: 'Gym' },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

// Gym Schema (simplified version for script)
const gymSchema = new mongoose.Schema({
  name: { type: String, required: true },
  subdomain: { type: String, required: true, unique: true },
  domain: { type: String },
  isActive: { type: Boolean, default: true }
});

const Gym = mongoose.model('Gym', gymSchema);

async function createTestUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find the test gym we created earlier
    const testGym = await Gym.findOne({ subdomain: 'testgym' });
    
    if (!testGym) {
      console.log('Test gym not found. Creating test gym first...');
      const newGym = new Gym({
        name: 'Test Gym',
        subdomain: 'testgym',
        domain: 'testgym.localhost',
        isActive: true
      });
      await newGym.save();
      console.log('Test gym created');
    }

    const gym = testGym || await Gym.findOne({ subdomain: 'testgym' });

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    
    if (existingUser) {
      console.log('Test user already exists:');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      console.log('Role: admin');
      console.log('Gym:', gym.name);
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 12);

    // Create test user
    const testUser = new User({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      gymId: gym._id,
      isActive: true
    });

    await testUser.save();

    console.log('✅ Test user created successfully!');
    console.log('');
    console.log('Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Role: admin');
    console.log('Gym:', gym.name);
    console.log('');
    console.log('You can now test login at:');
    console.log('- Default tenant: http://localhost:3000/auth/signin');
    console.log('- Test gym tenant: http://testgym.localhost:3000/auth/signin');

  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createTestUser();

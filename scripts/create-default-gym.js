const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
function loadEnvFile() {
  try {
    const envPath = path.join(__dirname, '..', '.env.local');
    const envFile = fs.readFileSync(envPath, 'utf8');
    
    envFile.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    });
  } catch (error) {
    console.error('Could not load .env.local file:', error.message);
  }
}

loadEnvFile();

// User Schema (simplified version for script)
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: { 
    type: String, 
    enum: ['admin', 'trainer', 'member'], 
    default: 'member' 
  },
  gymId: { type: mongoose.Schema.Types.ObjectId, ref: 'Gym' },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);

// Gym Schema (simplified version for script)
const gymSchema = new mongoose.Schema({
  name: { type: String, required: true },
  subdomain: { type: String, required: true, unique: true },
  domain: { type: String },
  isActive: { type: Boolean, default: true }
});

const Gym = mongoose.model('Gym', gymSchema);

async function createDefaultGym() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check if default gym already exists
    const existingGym = await Gym.findOne({ subdomain: 'default' });
    
    if (existingGym) {
      console.log('Default gym already exists');
      
      // Check if default user exists
      const existingUser = await User.findOne({ email: '<EMAIL>' });
      if (existingUser) {
        console.log('Default user already exists:');
        console.log('Email: <EMAIL>');
        console.log('Password: password123');
        console.log('Role: admin');
        console.log('Gym: GymD Platform');
        return;
      }
    }

    // Create default gym if it doesn't exist
    let defaultGym = existingGym;
    if (!defaultGym) {
      defaultGym = new Gym({
        name: 'GymD Platform',
        subdomain: 'default',
        domain: 'localhost:3000',
        isActive: true
      });
      await defaultGym.save();
      console.log('Default gym created');
    }

    // Create default admin user
    const hashedPassword = await bcrypt.hash('password123', 12);

    const defaultUser = new User({
      name: 'Platform Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      gymId: defaultGym._id,
      isActive: true
    });

    await defaultUser.save();

    console.log('✅ Default gym and user created successfully!');
    console.log('');
    console.log('Login credentials for localhost:3000:');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('Role: admin');
    console.log('Gym: GymD Platform');
    console.log('');
    console.log('You can now login at: http://localhost:3000/auth/signin');

  } catch (error) {
    console.error('Error creating default gym:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createDefaultGym();

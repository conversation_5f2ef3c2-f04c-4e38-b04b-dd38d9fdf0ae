const mongoose = require('mongoose');

// Simple Gym schema for testing
const GymSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  subdomain: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: /^[a-z0-9-]+$/,
    maxlength: 50,
  },
  domain: {
    type: String,
    trim: true,
  },
  logo: {
    type: String,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500,
  },
  address: {
    street: { type: String, required: true, trim: true },
    city: { type: String, required: true, trim: true },
    state: { type: String, required: true, trim: true },
    zipCode: { type: String, required: true, trim: true },
    country: { type: String, required: true, trim: true, default: 'US' },
  },
  contact: {
    phone: { type: String, required: true, trim: true },
    email: { type: String, required: true, trim: true, lowercase: true },
    website: { type: String, trim: true },
  },
  settings: {
    timezone: { type: String, default: 'America/New_York' },
    currency: { type: String, default: 'USD' },
    language: { type: String, default: 'en' },
    dateFormat: { type: String, default: 'MM/DD/YYYY' },
    businessHours: {
      type: Map,
      of: {
        open: { type: String, default: '06:00' },
        close: { type: String, default: '22:00' },
        isOpen: { type: Boolean, default: true },
      },
      default: () => new Map([
        ['monday', { open: '06:00', close: '22:00', isOpen: true }],
        ['tuesday', { open: '06:00', close: '22:00', isOpen: true }],
        ['wednesday', { open: '06:00', close: '22:00', isOpen: true }],
        ['thursday', { open: '06:00', close: '22:00', isOpen: true }],
        ['friday', { open: '06:00', close: '22:00', isOpen: true }],
        ['saturday', { open: '08:00', close: '20:00', isOpen: true }],
        ['sunday', { open: '08:00', close: '20:00', isOpen: true }],
      ]),
    },
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free',
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'cancelled'],
      default: 'active',
    },
    startDate: { type: Date, default: Date.now },
    endDate: Date,
    maxMembers: { type: Number, default: 50 },
    maxTrainers: { type: Number, default: 5 },
  },
  whatsapp: {
    enabled: { type: Boolean, default: false },
    accountSid: String,
    authToken: String,
    phoneNumber: String,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Pre-save middleware
GymSchema.pre('save', function(next) {
  if (this.isModified('subdomain') || this.isNew) {
    this.domain = `${this.subdomain}.localhost:3001`;
  }
  next();
});

const Gym = mongoose.model('Gym', GymSchema);

async function createTestGym() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/gymd');
    console.log('Connected to MongoDB');

    // Check if test gym already exists
    const existingGym = await Gym.findOne({ subdomain: 'testgym' });
    if (existingGym) {
      console.log('Test gym already exists:', existingGym.name);
      return;
    }

    // Create test gym
    const testGym = new Gym({
      name: 'Test Fitness Center',
      subdomain: 'testgym',
      description: 'A test gym for development purposes',
      address: {
        street: '123 Test Street',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'US',
      },
      contact: {
        phone: '******-0123',
        email: '<EMAIL>',
        website: 'https://testgym.com',
      },
    });

    await testGym.save();
    console.log('Test gym created successfully:', testGym.name);
    console.log('Subdomain:', testGym.subdomain);
    console.log('Domain:', testGym.domain);
    console.log('You can now test with: http://testgym.localhost:3001');

  } catch (error) {
    console.error('Error creating test gym:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createTestGym();

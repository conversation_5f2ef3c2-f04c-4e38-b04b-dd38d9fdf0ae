"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/multi-tenant/TenantProvider.tsx":
/*!********************************************************!*\
  !*** ./src/components/multi-tenant/TenantProvider.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TenantProvider: () => (/* binding */ TenantProvider),\n/* harmony export */   useTenant: () => (/* binding */ useTenant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTenant,TenantProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst TenantContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    tenant: null,\n    loading: true,\n    error: null\n});\nconst useTenant = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TenantContext);\n    if (!context) {\n        throw new Error('useTenant must be used within a TenantProvider');\n    }\n    return context;\n};\n_s(useTenant, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst TenantProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [tenant, setTenant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TenantProvider.useEffect\": ()=>{\n            const fetchTenantInfo = {\n                \"TenantProvider.useEffect.fetchTenantInfo\": async ()=>{\n                    try {\n                        const response = await fetch('/api/tenant');\n                        if (!response.ok) {\n                            throw new Error(\"Failed to fetch tenant information: \".concat(response.status));\n                        }\n                        const text = await response.text();\n                        console.log('Tenant API response text:', text);\n                        if (!text) {\n                            throw new Error('Empty response from tenant API');\n                        }\n                        const data = JSON.parse(text);\n                        console.log('Parsed tenant data:', data);\n                        setTenant(data.tenant);\n                    } catch (err) {\n                        console.error('Failed to fetch tenant info:', err);\n                        setError(err instanceof Error ? err.message : 'Unknown error');\n                        // Fallback to default tenant on error\n                        setTenant({\n                            id: 'default',\n                            subdomain: 'default',\n                            name: 'GymD Platform'\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"TenantProvider.useEffect.fetchTenantInfo\"];\n            fetchTenantInfo();\n        }\n    }[\"TenantProvider.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TenantContext.Provider, {\n        value: {\n            tenant,\n            loading,\n            error\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Projects/gymd/src/components/multi-tenant/TenantProvider.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(TenantProvider, \"xYq5tsfqJ0k8zNT9RMTSySNVnJs=\");\n_c = TenantProvider;\nvar _c;\n$RefreshReg$(_c, \"TenantProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/multi-tenant/TenantProvider.tsx\n"));

/***/ })

});
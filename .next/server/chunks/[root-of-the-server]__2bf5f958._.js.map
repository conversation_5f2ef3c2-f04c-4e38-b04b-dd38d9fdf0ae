{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\ninterface ConnectionCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongooseCache: ConnectionCache | undefined;\n}\n\nlet cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};\n\nif (!global.mongooseCache) {\n  global.mongooseCache = cached;\n}\n\nexport async function connectToDatabase(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4,\n    };\n\n    const mongoUri = process.env.MONGODB_URI;\n    if (!mongoUri) {\n      throw new Error('MONGODB_URI environment variable is not defined');\n    }\n\n    cached.promise = mongoose.connect(mongoUri, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n    console.log('✅ Connected to MongoDB');\n    return cached.conn;\n  } catch (error) {\n    cached.promise = null;\n    console.error('❌ MongoDB connection error:', error);\n    throw error;\n  }\n}\n\nexport async function disconnectFromDatabase(): Promise<void> {\n  if (cached.conn) {\n    await cached.conn.disconnect();\n    cached.conn = null;\n    cached.promise = null;\n    console.log('🔌 Disconnected from MongoDB');\n  }\n}\n\n// Connection event handlers\nmongoose.connection.on('connected', () => {\n  console.log('🔗 Mongoose connected to MongoDB');\n});\n\nmongoose.connection.on('error', (err) => {\n  console.error('❌ Mongoose connection error:', err);\n});\n\nmongoose.connection.on('disconnected', () => {\n  console.log('🔌 Mongoose disconnected from MongoDB');\n});\n\n// Graceful shutdown (only in Node.js runtime, not Edge Runtime)\n// Note: Removed process handlers to avoid Edge Runtime compatibility issues\n// The connection will be handled by MongoDB driver's built-in connection pooling\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,IAAI,SAA0B,OAAO,aAAa,IAAI;IACpD,MAAM;IACN,SAAS;AACX;AAEA,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG;AACzB;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;IAC9C;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;AACF;AAEA,4BAA4B;AAC5B,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa;IAClC,QAAQ,GAAG,CAAC;AACd;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;IAC/B,QAAQ,KAAK,CAAC,gCAAgC;AAChD;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB;IACrC,QAAQ,GAAG,CAAC;AACd,IAEA,gEAAgE;CAChE,4EAA4E;CAC5E,iFAAiF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\n// User roles enum\nexport enum UserRole {\n  SUPER_ADMIN = 'super_admin',\n  GYM_OWNER = 'gym_owner',\n  GYM_ADMIN = 'gym_admin',\n  GYM_STAFF = 'gym_staff',\n  MEMBER = 'member'\n}\n\n// User status enum\nexport enum UserStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  SUSPENDED = 'suspended',\n  PENDING = 'pending'\n}\n\n// User interface\nexport interface IUser extends Document {\n  // Basic Information\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  avatar?: string;\n  \n  // Role and Permissions\n  role: UserRole;\n  status: UserStatus;\n  \n  // Multi-tenant Association\n  gymId?: string; // Optional for super_admin, required for others\n  gymIds?: string[]; // For users who can access multiple gyms (super_admin, gym_owner)\n  \n  // Authentication\n  emailVerified?: Date;\n  emailVerificationToken?: string;\n  passwordResetToken?: string;\n  passwordResetExpires?: Date;\n  lastLogin?: Date;\n  loginAttempts?: number;\n  lockUntil?: Date;\n  \n  // Profile Information\n  dateOfBirth?: Date;\n  gender?: 'male' | 'female' | 'other';\n  address?: {\n    street?: string;\n    city?: string;\n    state?: string;\n    zipCode?: string;\n    country?: string;\n  };\n  \n  // Preferences\n  preferences?: {\n    notifications?: {\n      email?: boolean;\n      sms?: boolean;\n      whatsapp?: boolean;\n    };\n    language?: string;\n    timezone?: string;\n  };\n  \n  // Timestamps\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Virtual properties\n  fullName: string;\n  isLocked: boolean;\n  \n  // Methods\n  comparePassword(candidatePassword: string): Promise<boolean>;\n  generatePasswordResetToken(): string;\n  generateEmailVerificationToken(): string;\n  incrementLoginAttempts(): Promise<void>;\n  resetLoginAttempts(): Promise<void>;\n}\n\n// User schema\nconst UserSchema = new Schema<IUser>({\n  // Basic Information\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [8, 'Password must be at least 8 characters long'],\n    select: false // Don't include password in queries by default\n  },\n  firstName: {\n    type: String,\n    required: [true, 'First name is required'],\n    trim: true,\n    maxlength: [50, 'First name cannot exceed 50 characters']\n  },\n  lastName: {\n    type: String,\n    required: [true, 'Last name is required'],\n    trim: true,\n    maxlength: [50, 'Last name cannot exceed 50 characters']\n  },\n  phone: {\n    type: String,\n    trim: true,\n    match: [/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Please enter a valid phone number']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  \n  // Role and Permissions\n  role: {\n    type: String,\n    enum: Object.values(UserRole),\n    required: [true, 'User role is required'],\n    default: UserRole.MEMBER\n  },\n  status: {\n    type: String,\n    enum: Object.values(UserStatus),\n    default: UserStatus.PENDING\n  },\n  \n  // Multi-tenant Association\n  gymId: {\n    type: String,\n    ref: 'Gym',\n    required: function(this: IUser) {\n      // gymId is required for all roles except super_admin\n      return this.role !== UserRole.SUPER_ADMIN;\n    },\n    index: true\n  },\n  gymIds: [{\n    type: String,\n    ref: 'Gym'\n  }],\n  \n  // Authentication\n  emailVerified: {\n    type: Date\n  },\n  emailVerificationToken: {\n    type: String,\n    select: false\n  },\n  passwordResetToken: {\n    type: String,\n    select: false\n  },\n  passwordResetExpires: {\n    type: Date,\n    select: false\n  },\n  lastLogin: {\n    type: Date\n  },\n  loginAttempts: {\n    type: Number,\n    default: 0\n  },\n  lockUntil: {\n    type: Date\n  },\n  \n  // Profile Information\n  dateOfBirth: {\n    type: Date\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other']\n  },\n  address: {\n    street: { type: String, trim: true },\n    city: { type: String, trim: true },\n    state: { type: String, trim: true },\n    zipCode: { type: String, trim: true },\n    country: { type: String, trim: true, default: 'US' }\n  },\n  \n  // Preferences\n  preferences: {\n    notifications: {\n      email: { type: Boolean, default: true },\n      sms: { type: Boolean, default: false },\n      whatsapp: { type: Boolean, default: false }\n    },\n    language: { type: String, default: 'en' },\n    timezone: { type: String, default: 'UTC' }\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for performance\nUserSchema.index({ email: 1 });\nUserSchema.index({ gymId: 1, role: 1 });\nUserSchema.index({ status: 1 });\nUserSchema.index({ createdAt: -1 });\n\n// Virtual properties\nUserSchema.virtual('fullName').get(function(this: IUser) {\n  return `${this.firstName} ${this.lastName}`.trim();\n});\n\nUserSchema.virtual('isLocked').get(function(this: IUser) {\n  return !!(this.lockUntil && this.lockUntil > new Date());\n});\n\n// Pre-save middleware\nUserSchema.pre('save', async function(this: IUser, next) {\n  // Only hash password if it's modified\n  if (!this.isModified('password')) return next();\n  \n  try {\n    // Hash password with cost of 12\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Pre-save middleware for gym association\nUserSchema.pre('save', function(this: IUser, next) {\n  // For super_admin, ensure gymIds array exists\n  if (this.role === UserRole.SUPER_ADMIN && !this.gymIds) {\n    this.gymIds = [];\n  }\n  \n  // For gym_owner, add gymId to gymIds array if not already present\n  if (this.role === UserRole.GYM_OWNER && this.gymId) {\n    if (!this.gymIds) this.gymIds = [];\n    if (!this.gymIds.includes(this.gymId)) {\n      this.gymIds.push(this.gymId);\n    }\n  }\n  \n  next();\n});\n\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  if (!this.password) return false;\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\nUserSchema.methods.generatePasswordResetToken = function(): string {\n  const resetToken = Math.random().toString(36).substring(2, 15) + \n                    Math.random().toString(36).substring(2, 15);\n  \n  this.passwordResetToken = resetToken;\n  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\n  \n  return resetToken;\n};\n\nUserSchema.methods.generateEmailVerificationToken = function(): string {\n  const verificationToken = Math.random().toString(36).substring(2, 15) + \n                           Math.random().toString(36).substring(2, 15);\n  \n  this.emailVerificationToken = verificationToken;\n  \n  return verificationToken;\n};\n\nUserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {\n  // If we have a previous lock that has expired, restart at 1\n  if (this.lockUntil && this.lockUntil < new Date()) {\n    return this.updateOne({\n      $unset: { lockUntil: 1 },\n      $set: { loginAttempts: 1 }\n    });\n  }\n  \n  const updates: any = { $inc: { loginAttempts: 1 } };\n  \n  // Lock account after 5 failed attempts for 2 hours\n  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {\n    updates.$set = { lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000) };\n  }\n  \n  return this.updateOne(updates);\n};\n\nUserSchema.methods.resetLoginAttempts = async function(): Promise<void> {\n  return this.updateOne({\n    $unset: { loginAttempts: 1, lockUntil: 1 },\n    $set: { lastLogin: new Date() }\n  });\n};\n\n// Export the model\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,IAAA,AAAK,kCAAA;;;;;;WAAA;;AASL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAwEZ,cAAc;AACd,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,oBAAoB;IACpB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAA8C;QAC7D,QAAQ,MAAM,+CAA+C;IAC/D;IACA,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAI;SAAwC;IAC1D;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAsB;SAAoC;IACpE;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IAEA,uBAAuB;IACvB,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC;QACpB,UAAU;YAAC;YAAM;SAAwB;QACzC,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,MAAM,OAAO,MAAM,CAAC;QACpB,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAO;QACL,MAAM;QACN,KAAK;QACL,UAAU;YACR,qDAAqD;YACrD,OAAO,IAAI,CAAC,IAAI;QAClB;QACA,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,KAAK;QACP;KAAE;IAEF,iBAAiB;IACjB,eAAe;QACb,MAAM;IACR;IACA,wBAAwB;QACtB,MAAM;QACN,QAAQ;IACV;IACA,oBAAoB;QAClB,MAAM;QACN,QAAQ;IACV;IACA,sBAAsB;QACpB,MAAM;QACN,QAAQ;IACV;IACA,WAAW;QACT,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;IAEA,sBAAsB;IACtB,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;SAAQ;IACnC;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,MAAM;QAAK;QACnC,MAAM;YAAE,MAAM;YAAQ,MAAM;QAAK;QACjC,OAAO;YAAE,MAAM;YAAQ,MAAM;QAAK;QAClC,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;QACpC,SAAS;YAAE,MAAM;YAAQ,MAAM;YAAM,SAAS;QAAK;IACrD;IAEA,cAAc;IACd,aAAa;QACX,eAAe;YACb,OAAO;gBAAE,MAAM;gBAAS,SAAS;YAAK;YACtC,KAAK;gBAAE,MAAM;gBAAS,SAAS;YAAM;YACrC,UAAU;gBAAE,MAAM;gBAAS,SAAS;YAAM;QAC5C;QACA,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;IAC3C;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,0BAA0B;AAC1B,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,OAAO;IAAG,MAAM;AAAE;AACrC,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AAEjC,qBAAqB;AACrB,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI;AAClD;AAEA,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM;AACzD;AAEA,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,eAA4B,IAAI;IACrD,sCAAsC;IACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,gCAAgC;QAChC,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0CAA0C;AAC1C,WAAW,GAAG,CAAC,QAAQ,SAAsB,IAAI;IAC/C,8CAA8C;IAC9C,IAAI,IAAI,CAAC,IAAI,sBAA6B,CAAC,IAAI,CAAC,MAAM,EAAE;QACtD,IAAI,CAAC,MAAM,GAAG,EAAE;IAClB;IAEA,kEAAkE;IAClE,IAAI,IAAI,CAAC,IAAI,oBAA2B,IAAI,CAAC,KAAK,EAAE;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QAC7B;IACF;IAEA;AACF;AAEA,mBAAmB;AACnB,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;IAC3B,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACzC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAE1D,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,aAAa;IAEhF,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM,oBAAoB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACzC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAEjE,IAAI,CAAC,sBAAsB,GAAG;IAE9B,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,sBAAsB,GAAG;IAC1C,4DAA4D;IAC5D,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ;gBAAE,WAAW;YAAE;YACvB,MAAM;gBAAE,eAAe;YAAE;QAC3B;IACF;IAEA,MAAM,UAAe;QAAE,MAAM;YAAE,eAAe;QAAE;IAAE;IAElD,mDAAmD;IACnD,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD,QAAQ,IAAI,GAAG;YAAE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK;QAAM;IACxE;IAEA,OAAO,IAAI,CAAC,SAAS,CAAC;AACxB;AAEA,WAAW,OAAO,CAAC,kBAAkB,GAAG;IACtC,OAAO,IAAI,CAAC,SAAS,CAAC;QACpB,QAAQ;YAAE,eAAe;YAAG,WAAW;QAAE;QACzC,MAAM;YAAE,WAAW,IAAI;QAAO;IAChC;AACF;uCAGe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/Gym.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IGym extends Document {\n  _id: string;\n  name: string;\n  subdomain: string;\n  domain: string;\n  logo?: string;\n  description?: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    zipCode: string;\n    country: string;\n  };\n  contact: {\n    phone: string;\n    email: string;\n    website?: string;\n  };\n  settings: {\n    timezone: string;\n    currency: string;\n    language: string;\n    dateFormat: string;\n    businessHours: {\n      [key: string]: {\n        open: string;\n        close: string;\n        isOpen: boolean;\n      };\n    };\n  };\n  subscription: {\n    plan: 'free' | 'basic' | 'premium' | 'enterprise';\n    status: 'active' | 'inactive' | 'suspended' | 'cancelled';\n    startDate: Date;\n    endDate?: Date;\n    maxMembers: number;\n    maxTrainers: number;\n  };\n  whatsapp: {\n    enabled: boolean;\n    accountSid?: string;\n    authToken?: string;\n    phoneNumber?: string;\n  };\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst GymSchema = new Schema<IGym>({\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: 100,\n  },\n  subdomain: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: /^[a-z0-9-]+$/,\n    maxlength: 50,\n  },\n  domain: {\n    type: String,\n    trim: true,\n  },\n  logo: {\n    type: String,\n    trim: true,\n  },\n  description: {\n    type: String,\n    trim: true,\n    maxlength: 500,\n  },\n  address: {\n    street: { type: String, required: true, trim: true },\n    city: { type: String, required: true, trim: true },\n    state: { type: String, required: true, trim: true },\n    zipCode: { type: String, required: true, trim: true },\n    country: { type: String, required: true, trim: true, default: 'US' },\n  },\n  contact: {\n    phone: { type: String, required: true, trim: true },\n    email: { type: String, required: true, trim: true, lowercase: true },\n    website: { type: String, trim: true },\n  },\n  settings: {\n    timezone: { type: String, default: 'America/New_York' },\n    currency: { type: String, default: 'USD' },\n    language: { type: String, default: 'en' },\n    dateFormat: { type: String, default: 'MM/DD/YYYY' },\n    businessHours: {\n      type: Map,\n      of: {\n        open: { type: String, default: '06:00' },\n        close: { type: String, default: '22:00' },\n        isOpen: { type: Boolean, default: true },\n      },\n      default: {\n        monday: { open: '06:00', close: '22:00', isOpen: true },\n        tuesday: { open: '06:00', close: '22:00', isOpen: true },\n        wednesday: { open: '06:00', close: '22:00', isOpen: true },\n        thursday: { open: '06:00', close: '22:00', isOpen: true },\n        friday: { open: '06:00', close: '22:00', isOpen: true },\n        saturday: { open: '08:00', close: '20:00', isOpen: true },\n        sunday: { open: '08:00', close: '20:00', isOpen: true },\n      },\n    },\n  },\n  subscription: {\n    plan: {\n      type: String,\n      enum: ['free', 'basic', 'premium', 'enterprise'],\n      default: 'free',\n    },\n    status: {\n      type: String,\n      enum: ['active', 'inactive', 'suspended', 'cancelled'],\n      default: 'active',\n    },\n    startDate: { type: Date, default: Date.now },\n    endDate: Date,\n    maxMembers: { type: Number, default: 50 },\n    maxTrainers: { type: Number, default: 5 },\n  },\n  whatsapp: {\n    enabled: { type: Boolean, default: false },\n    accountSid: String,\n    authToken: String,\n    phoneNumber: String,\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true },\n});\n\n// Indexes\nGymSchema.index({ subdomain: 1 });\nGymSchema.index({ domain: 1 });\nGymSchema.index({ isActive: 1 });\nGymSchema.index({ 'subscription.status': 1 });\n\n// Virtual for full address\nGymSchema.virtual('fullAddress').get(function() {\n  return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;\n});\n\n// Pre-save middleware\nGymSchema.pre('save', function(next) {\n  if (this.isModified('subdomain') || this.isNew) {\n    this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;\n  }\n  next();\n});\n\nexport default mongoose.models.Gym || mongoose.model<IGym>('Gym', GymSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqDA,MAAM,YAAY,IAAI,yGAAA,CAAA,SAAM,CAAO;IACjC,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;IACb;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACnD,MAAM;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACjD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACpD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,SAAS;QAAK;IACrE;IACA,SAAS;QACP,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,WAAW;QAAK;QACnE,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;IACtC;IACA,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAmB;QACtD,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAa;QAClD,eAAe;YACb,MAAM;YACN,IAAI;gBACF,MAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACvC,OAAO;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACxC,QAAQ;oBAAE,MAAM;oBAAS,SAAS;gBAAK;YACzC;YACA,SAAS;gBACP,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACvD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACzD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;YACxD;QACF;IACF;IACA,cAAc;QACZ,MAAM;YACJ,MAAM;YACN,MAAM;gBAAC;gBAAQ;gBAAS;gBAAW;aAAa;YAChD,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,MAAM;gBAAC;gBAAU;gBAAY;gBAAa;aAAY;YACtD,SAAS;QACX;QACA,WAAW;YAAE,MAAM;YAAM,SAAS,KAAK,GAAG;QAAC;QAC3C,SAAS;QACT,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAG;QACxC,aAAa;YAAE,MAAM;YAAQ,SAAS;QAAE;IAC1C;IACA,UAAU;QACR,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,UAAU,KAAK,CAAC;IAAE,WAAW;AAAE;AAC/B,UAAU,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC5B,UAAU,KAAK,CAAC;IAAE,UAAU;AAAE;AAC9B,UAAU,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAE3C,2BAA2B;AAC3B,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;IACnC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC/H;AAEA,sBAAsB;AACtB,UAAU,GAAG,CAAC,QAAQ,SAAS,IAAI;IACjC,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,CAAC,KAAK,EAAE;QAC9C,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,UAAU,IAAI,kBAAkB;IACjF;IACA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAO,OAAO", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/validations/schemas.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// Common schemas\nexport const emailSchema = z.string().email('Invalid email address');\nexport const phoneSchema = z.string().min(10, 'Phone number must be at least 10 digits');\nexport const passwordSchema = z.string().min(8, 'Password must be at least 8 characters');\n\n// Gym schema\nexport const gymSchema = z.object({\n  name: z.string().min(1, 'Gym name is required').max(100, 'Gym name too long'),\n  subdomain: z.string()\n    .min(3, 'Subdomain must be at least 3 characters')\n    .max(50, 'Subdomain too long')\n    .regex(/^[a-z0-9-]+$/, 'Subdomain can only contain lowercase letters, numbers, and hyphens'),\n  description: z.string().max(500, 'Description too long').optional(),\n  address: z.object({\n    street: z.string().min(1, 'Street address is required'),\n    city: z.string().min(1, 'City is required'),\n    state: z.string().min(1, 'State is required'),\n    zipCode: z.string().min(1, 'ZIP code is required'),\n    country: z.string().default('US'),\n  }),\n  contact: z.object({\n    phone: phoneSchema,\n    email: emailSchema,\n    website: z.string().url('Invalid website URL').optional(),\n  }),\n});\n\n// Package schema\nexport const packageSchema = z.object({\n  gymId: z.string().min(1, 'Gym ID is required'),\n  name: z.string().min(1, 'Package name is required').max(100, 'Package name too long'),\n  description: z.string().max(500, 'Description too long').optional(),\n  type: z.enum(['monthly', 'quarterly', 'yearly', 'custom']),\n  duration: z.object({\n    value: z.number().min(1, 'Duration value must be at least 1'),\n    unit: z.enum(['days', 'weeks', 'months', 'years']),\n  }),\n  pricing: z.object({\n    amount: z.number().min(0, 'Amount must be non-negative'),\n    currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),\n    setupFee: z.number().min(0, 'Setup fee must be non-negative').optional(),\n  }),\n  features: z.array(z.string()).default([]),\n  benefits: z.array(z.string()).default([]),\n  restrictions: z.object({\n    maxClasses: z.number().min(0).optional(),\n    maxTrainerSessions: z.number().min(0).optional(),\n    accessHours: z.object({\n      start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n      end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n    }).optional(),\n    allowedDays: z.array(z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])).optional(),\n  }).optional(),\n  isActive: z.boolean().default(true),\n  isPopular: z.boolean().default(false),\n  sortOrder: z.number().default(0),\n});\n\n// Member schema\nexport const memberSchema = z.object({\n  personalInfo: z.object({\n    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),\n    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),\n    email: emailSchema,\n    phone: phoneSchema,\n    dateOfBirth: z.string().transform((str) => new Date(str)).optional(),\n    gender: z.enum(['male', 'female', 'other']).optional(),\n    profilePhoto: z.string().url('Invalid photo URL').optional(),\n  }),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().default('US'),\n  }).optional(),\n  emergencyContact: z.object({\n    name: z.string().min(1, 'Emergency contact name is required'),\n    relationship: z.string().min(1, 'Relationship is required'),\n    phone: phoneSchema,\n  }),\n  healthInfo: z.object({\n    medicalConditions: z.array(z.string()).default([]),\n    allergies: z.array(z.string()).default([]),\n    medications: z.array(z.string()).default([]),\n    fitnessGoals: z.array(z.string()).default([]),\n    notes: z.string().max(1000, 'Notes too long').optional(),\n  }).optional(),\n  preferences: z.object({\n    preferredTrainers: z.array(z.string()).default([]),\n    notifications: z.object({\n      email: z.boolean().default(true),\n      sms: z.boolean().default(false),\n      whatsapp: z.boolean().default(false),\n    }),\n    language: z.string().default('en'),\n  }).optional(),\n});\n\n// Trainer schema\nexport const trainerSchema = z.object({\n  personalInfo: z.object({\n    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),\n    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),\n    email: emailSchema,\n    phone: phoneSchema,\n    dateOfBirth: z.string().transform((str) => new Date(str)).optional(),\n    gender: z.enum(['male', 'female', 'other']).optional(),\n    profilePhoto: z.string().url('Invalid photo URL').optional(),\n  }),\n  professional: z.object({\n    specializations: z.array(z.string()).default([]),\n    certifications: z.array(z.object({\n      name: z.string().min(1, 'Certification name is required'),\n      issuedBy: z.string().min(1, 'Issuing organization is required'),\n      issuedDate: z.string().transform((str) => new Date(str)),\n      expiryDate: z.string().transform((str) => new Date(str)).optional(),\n      certificateUrl: z.string().url('Invalid certificate URL').optional(),\n    })).default([]),\n    experience: z.number().min(0, 'Experience must be non-negative').default(0),\n    bio: z.string().max(1000, 'Bio too long').optional(),\n    hourlyRate: z.number().min(0, 'Hourly rate must be non-negative').optional(),\n  }),\n  employment: z.object({\n    hireDate: z.string().transform((str) => new Date(str)).default(() => new Date()),\n    employmentType: z.enum(['full-time', 'part-time', 'contract', 'freelance']),\n    salary: z.number().min(0, 'Salary must be non-negative').optional(),\n  }),\n});\n\n// Subscription schema\nexport const subscriptionSchema = z.object({\n  memberId: z.string().min(1, 'Member ID is required'),\n  packageId: z.string().min(1, 'Package ID is required'),\n  startDate: z.string().transform((str) => new Date(str)),\n  payment: z.object({\n    amount: z.number().min(0, 'Amount must be non-negative'),\n    currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),\n    setupFee: z.number().min(0, 'Setup fee must be non-negative').optional(),\n    paymentMethod: z.enum(['cash', 'card', 'bank_transfer', 'online', 'other']),\n    transactionId: z.string().optional(),\n  }),\n  autoRenewal: z.object({\n    enabled: z.boolean().default(false),\n  }).optional(),\n  notes: z.string().max(500, 'Notes too long').optional(),\n});\n\n// API query schemas\nexport const paginationSchema = z.object({\n  page: z.string().transform((val) => parseInt(val, 10)).default('1'),\n  limit: z.string().transform((val) => parseInt(val, 10)).default('10'),\n  search: z.string().optional(),\n  sortBy: z.string().optional(),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n});\n\nexport const idParamSchema = z.object({\n  id: z.string().min(1, 'ID is required'),\n});\n\n// User role enum\nexport const userRoleSchema = z.enum(['super_admin', 'gym_owner', 'gym_admin', 'gym_staff', 'member']);\n\n// User status enum\nexport const userStatusSchema = z.enum(['active', 'inactive', 'suspended', 'pending']);\n\n// User registration schema\nexport const userRegistrationSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n  confirmPassword: z.string(),\n  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),\n  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),\n  phone: z.string().regex(/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Invalid phone number').optional(),\n  role: userRoleSchema.default('member'),\n  gymId: z.string().optional(),\n  dateOfBirth: z.string().datetime().optional(),\n  gender: z.enum(['male', 'female', 'other']).optional(),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().default('US')\n  }).optional()\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n});\n\n// User login schema\nexport const userLoginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n  gymSubdomain: z.string().optional(),\n  rememberMe: z.boolean().default(false)\n});\n\n// User profile update schema\nexport const userProfileUpdateSchema = z.object({\n  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long').optional(),\n  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long').optional(),\n  phone: z.string().regex(/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Invalid phone number').optional(),\n  avatar: z.string().url('Invalid avatar URL').optional(),\n  dateOfBirth: z.string().datetime().optional(),\n  gender: z.enum(['male', 'female', 'other']).optional(),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().optional()\n  }).optional(),\n  preferences: z.object({\n    notifications: z.object({\n      email: z.boolean().optional(),\n      sms: z.boolean().optional(),\n      whatsapp: z.boolean().optional()\n    }).optional(),\n    language: z.string().optional(),\n    timezone: z.string().optional()\n  }).optional()\n});\n\n// User admin update schema (for admin operations)\nexport const userAdminUpdateSchema = userProfileUpdateSchema.extend({\n  role: userRoleSchema.optional(),\n  status: userStatusSchema.optional(),\n  gymId: z.string().optional(),\n  gymIds: z.array(z.string()).optional()\n});\n\n// Password change schema\nexport const passwordChangeSchema = z.object({\n  currentPassword: z.string().min(1, 'Current password is required'),\n  newPassword: z.string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n  confirmNewPassword: z.string()\n}).refine((data) => data.newPassword === data.confirmNewPassword, {\n  message: \"New passwords don't match\",\n  path: [\"confirmNewPassword\"],\n});\n\n// Password reset request schema\nexport const passwordResetRequestSchema = z.object({\n  email: z.string().email('Invalid email address')\n});\n\n// Password reset schema\nexport const passwordResetSchema = z.object({\n  token: z.string().min(1, 'Reset token is required'),\n  password: z.string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n  confirmPassword: z.string()\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n});\n\n// Email verification schema\nexport const emailVerificationSchema = z.object({\n  token: z.string().min(1, 'Verification token is required')\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AAGO,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AACrC,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;AACvC,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAGzC,MAAM,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,KAAK;IACzD,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAChB,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,IAAI,sBACR,KAAK,CAAC,gBAAgB;IACzB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;IACjE,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACzB,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC3B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B;IACA,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,OAAO;QACP,OAAO;QACP,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,uBAAuB,QAAQ;IACzD;AACF;AAGO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,KAAK;IAC7D,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;IACjE,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAa;QAAU;KAAS;IACzD,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAS;YAAU;SAAQ;IACnD;IACA,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,iCAAiC,OAAO,CAAC;QACxE,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,kCAAkC,QAAQ;IACxE;IACA,UAAU,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACxC,UAAU,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACxC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;QACtC,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;QAC9C,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACpB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;YAC7D,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;QAC7D,GAAG,QAAQ;QACX,aAAa,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAW;YAAa;YAAY;YAAU;YAAY;SAAS,GAAG,QAAQ;IACvH,GAAG,QAAQ;IACX,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,WAAW,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC;AAGO,MAAM,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI;QAC/D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI;QAC7D,OAAO;QACP,OAAO;QACP,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;QAClE,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAQ,EAAE,QAAQ;QACpD,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,qBAAqB,QAAQ;IAC5D;IACA,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B,GAAG,QAAQ;IACX,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,OAAO;IACT;IACA,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,mBAAmB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QACjD,WAAW,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QACzC,aAAa,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC3C,cAAc,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC5C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,kBAAkB,QAAQ;IACxD,GAAG,QAAQ;IACX,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,mBAAmB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QACjD,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACtB,OAAO,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B,KAAK,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QAChC;QACA,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC/B,GAAG,QAAQ;AACb;AAGO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI;QAC/D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI;QAC7D,OAAO;QACP,OAAO;QACP,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;QAClE,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAQ,EAAE,QAAQ;QACpD,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,qBAAqB,QAAQ;IAC5D;IACA,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,iBAAiB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC/C,gBAAgB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YAC/B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC5B,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK;YACnD,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;YACjE,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,2BAA2B,QAAQ;QACpE,IAAI,OAAO,CAAC,EAAE;QACd,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,mCAAmC,OAAO,CAAC;QACzE,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,gBAAgB,QAAQ;QAClD,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,oCAAoC,QAAQ;IAC5E;IACA,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,OAAO,CAAC,IAAM,IAAI;QACzE,gBAAgB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAa;YAAa;YAAY;SAAY;QAC1E,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,+BAA+B,QAAQ;IACnE;AACF;AAGO,MAAM,qBAAqB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK;IAClD,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,iCAAiC,OAAO,CAAC;QACxE,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,kCAAkC,QAAQ;QACtE,eAAe,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAQ;YAAiB;YAAU;SAAQ;QAC1E,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC;IACA,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,SAAS,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,GAAG,QAAQ;IACX,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;AACvD;AAGO,MAAM,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,SAAS,KAAK,KAAK,OAAO,CAAC;IAC/D,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,SAAS,KAAK,KAAK,OAAO,CAAC;IAChE,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;AAC7C;AAEO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACxB;AAGO,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAe;IAAa;IAAa;IAAa;CAAS;AAG9F,MAAM,mBAAmB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAU;IAAY;IAAa;CAAU;AAG9E,MAAM,yBAAyB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,mCAAmC;IAC5C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM;IACzB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI;IAC/D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI;IAC7D,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,sBAAsB,wBAAwB,QAAQ;IAC9E,MAAM,eAAe,OAAO,CAAC;IAC7B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3C,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ,EAAE,QAAQ;IACpD,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B,GAAG,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,YAAY,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAClC;AAGO,MAAM,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI,uBAAuB,QAAQ;IAC9F,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI,sBAAsB,QAAQ;IAC3F,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,sBAAsB,wBAAwB,QAAQ;IAC9E,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,sBAAsB,QAAQ;IACrD,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3C,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ,EAAE,QAAQ;IACpD,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,GAAG,QAAQ;IACX,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACtB,OAAO,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YAC3B,KAAK,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QAChC,GAAG,QAAQ;QACX,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,GAAG,QAAQ;AACb;AAGO,MAAM,wBAAwB,wBAAwB,MAAM,CAAC;IAClE,MAAM,eAAe,QAAQ;IAC7B,QAAQ,iBAAiB,QAAQ;IACjC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AACtC;AAGO,MAAM,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,mCAAmC;IAC5C,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM;AAC9B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,kBAAkB,EAAE;IAChE,SAAS;IACT,MAAM;QAAC;KAAqB;AAC9B;AAGO,MAAM,6BAA6B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjD,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAGO,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,mCAAmC;IAC5C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/utils/api.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { ZodError } from 'zod';\nimport { ApiResponse, PaginatedResponse } from '@/types';\n\n/**\n * Create a success response\n */\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  metadata?: any,\n  status: number = 200\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      success: true,\n      data,\n      message,\n      ...metadata\n    },\n    { status }\n  );\n}\n\n/**\n * Create an error response\n */\nexport function createErrorResponse(\n  error: string,\n  status: number = 400,\n  details?: any\n): NextResponse<ApiResponse> {\n  return NextResponse.json(\n    {\n      success: false,\n      error,\n      ...(details && { details }),\n    },\n    { status }\n  );\n}\n\n/**\n * Create a paginated response\n */\nexport function createPaginatedResponse<T>(\n  data: T[],\n  page: number,\n  limit: number,\n  total: number,\n  message?: string\n): NextResponse<PaginatedResponse<T>> {\n  const totalPages = Math.ceil(total / limit);\n\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination: {\n      page,\n      limit,\n      total,\n      totalPages,\n    },\n  });\n}\n\n/**\n * Handle validation errors from Zod\n */\nexport function handleValidationError(error: ZodError): NextResponse<ApiResponse> {\n  const formattedErrors = error.errors.map((err) => ({\n    field: err.path.join('.'),\n    message: err.message,\n  }));\n\n  return createErrorResponse(\n    'Validation failed',\n    400,\n    { validationErrors: formattedErrors }\n  );\n}\n\n/**\n * Handle database errors\n */\nexport function handleDatabaseError(error: any): NextResponse<ApiResponse> {\n  console.error('Database error:', error);\n\n  // Handle MongoDB duplicate key error\n  if (error.code === 11000) {\n    const field = Object.keys(error.keyPattern || {})[0] || 'field';\n    return createErrorResponse(`${field} already exists`, 409);\n  }\n\n  // Handle validation errors\n  if (error.name === 'ValidationError') {\n    const validationErrors = Object.values(error.errors).map((err: any) => ({\n      field: err.path,\n      message: err.message,\n    }));\n    return createErrorResponse('Validation failed', 400, { validationErrors });\n  }\n\n  // Handle cast errors (invalid ObjectId, etc.)\n  if (error.name === 'CastError') {\n    return createErrorResponse('Invalid ID format', 400);\n  }\n\n  // Generic database error\n  return createErrorResponse('Database operation failed', 500);\n}\n\n/**\n * Handle async route errors\n */\nexport function withErrorHandling(\n  handler: (request: Request, context?: any) => Promise<NextResponse>\n) {\n  return async (request: Request, context?: any): Promise<NextResponse> => {\n    try {\n      return await handler(request, context);\n    } catch (error) {\n      console.error('API route error:', error);\n\n      if (error instanceof ZodError) {\n        return handleValidationError(error);\n      }\n\n      if (error && typeof error === 'object' && 'code' in error) {\n        return handleDatabaseError(error);\n      }\n\n      return createErrorResponse(\n        error instanceof Error ? error.message : 'Internal server error',\n        500\n      );\n    }\n  };\n}\n\n/**\n * Extract pagination parameters from URL search params\n */\nexport function extractPaginationParams(searchParams: URLSearchParams) {\n  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));\n  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10', 10)));\n  const search = searchParams.get('search') || undefined;\n  const sortBy = searchParams.get('sortBy') || 'createdAt';\n  const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';\n\n  return {\n    page,\n    limit,\n    skip: (page - 1) * limit,\n    search,\n    sortBy,\n    sortOrder,\n  };\n}\n\n/**\n * Build MongoDB sort object\n */\nexport function buildSortObject(sortBy: string, sortOrder: 'asc' | 'desc') {\n  return { [sortBy]: sortOrder === 'asc' ? 1 : -1 };\n}\n\n/**\n * Build MongoDB search filter\n */\nexport function buildSearchFilter(search: string | undefined, fields: string[]) {\n  if (!search) return {};\n\n  const searchRegex = new RegExp(search, 'i');\n  return {\n    $or: fields.map((field) => ({ [field]: searchRegex })),\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AAAA;;;AAMO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,QAAc,EACd,SAAiB,GAAG;IAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT;QACA;QACA,GAAG,QAAQ;IACb,GACA;QAAE;IAAO;AAEb;AAKO,SAAS,oBACd,KAAa,EACb,SAAiB,GAAG,EACpB,OAAa;IAEb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT;QACA,GAAI,WAAW;YAAE;QAAQ,CAAC;IAC5B,GACA;QAAE;IAAO;AAEb;AAKO,SAAS,wBACd,IAAS,EACT,IAAY,EACZ,KAAa,EACb,KAAa,EACb,OAAgB;IAEhB,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA,YAAY;YACV;YACA;YACA;YACA;QACF;IACF;AACF;AAKO,SAAS,sBAAsB,KAAe;IACnD,MAAM,kBAAkB,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAC;YACjD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;YACrB,SAAS,IAAI,OAAO;QACtB,CAAC;IAED,OAAO,oBACL,qBACA,KACA;QAAE,kBAAkB;IAAgB;AAExC;AAKO,SAAS,oBAAoB,KAAU;IAC5C,QAAQ,KAAK,CAAC,mBAAmB;IAEjC,qCAAqC;IACrC,IAAI,MAAM,IAAI,KAAK,OAAO;QACxB,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI;QACxD,OAAO,oBAAoB,GAAG,MAAM,eAAe,CAAC,EAAE;IACxD;IAEA,2BAA2B;IAC3B,IAAI,MAAM,IAAI,KAAK,mBAAmB;QACpC,MAAM,mBAAmB,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAa,CAAC;gBACtE,OAAO,IAAI,IAAI;gBACf,SAAS,IAAI,OAAO;YACtB,CAAC;QACD,OAAO,oBAAoB,qBAAqB,KAAK;YAAE;QAAiB;IAC1E;IAEA,8CAA8C;IAC9C,IAAI,MAAM,IAAI,KAAK,aAAa;QAC9B,OAAO,oBAAoB,qBAAqB;IAClD;IAEA,yBAAyB;IACzB,OAAO,oBAAoB,6BAA6B;AAC1D;AAKO,SAAS,kBACd,OAAmE;IAEnE,OAAO,OAAO,SAAkB;QAC9B,IAAI;YACF,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAElC,IAAI,iBAAiB,sJAAA,CAAA,WAAQ,EAAE;gBAC7B,OAAO,sBAAsB;YAC/B;YAEA,IAAI,SAAS,OAAO,UAAU,YAAY,UAAU,OAAO;gBACzD,OAAO,oBAAoB;YAC7B;YAEA,OAAO,oBACL,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC;QAEJ;IACF;AACF;AAKO,SAAS,wBAAwB,YAA6B;IACnE,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,WAAW,KAAK;IACnE,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,YAAY,MAAM;IACpF,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;IAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;IAC7C,MAAM,YAAa,aAAa,GAAG,CAAC,gBAAgB;IAEpD,OAAO;QACL;QACA;QACA,MAAM,CAAC,OAAO,CAAC,IAAI;QACnB;QACA;QACA;IACF;AACF;AAKO,SAAS,gBAAgB,MAAc,EAAE,SAAyB;IACvE,OAAO;QAAE,CAAC,OAAO,EAAE,cAAc,QAAQ,IAAI,CAAC;IAAE;AAClD;AAKO,SAAS,kBAAkB,MAA0B,EAAE,MAAgB;IAC5E,IAAI,CAAC,QAAQ,OAAO,CAAC;IAErB,MAAM,cAAc,IAAI,OAAO,QAAQ;IACvC,OAAO;QACL,KAAK,OAAO,GAAG,CAAC,CAAC,QAAU,CAAC;gBAAE,CAAC,MAAM,EAAE;YAAY,CAAC;IACtD;AACF", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { connectToDatabase } from '@/lib/database/connection';\nimport User, { UserRole, UserStatus } from '@/models/User';\nimport Gym from '@/models/Gym';\nimport { userRegistrationSchema } from '@/lib/validations/schemas';\nimport { \n  createSuccessResponse, \n  createErrorResponse,\n  withErrorHandling\n} from '@/lib/utils/api';\n\n/**\n * POST /api/auth/register - Register new user\n */\nexport const POST = withErrorHandling(async (request: NextRequest) => {\n  await connectToDatabase();\n  \n  const body = await request.json();\n  \n  // Validate request body\n  const validatedData = userRegistrationSchema.parse(body);\n  \n  // Check if user already exists\n  const existingUser = await User.findOne({ \n    email: validatedData.email.toLowerCase() \n  });\n  \n  if (existingUser) {\n    return createErrorResponse('User with this email already exists', 409);\n  }\n  \n  // Validate gym association for non-super-admin users\n  if (validatedData.role !== UserRole.SUPER_ADMIN && !validatedData.gymId) {\n    return createErrorResponse('gymId is required for gym registration', 400);\n  }\n  \n  // Verify gym exists if gymId is provided\n  if (validatedData.gymId) {\n    const gym = await Gym.findById(validatedData.gymId);\n    if (!gym) {\n      return createErrorResponse('Invalid gym ID', 400);\n    }\n  }\n  \n  // Prevent direct super admin registration (must be created by existing super admin)\n  if (validatedData.role === UserRole.SUPER_ADMIN) {\n    return createErrorResponse('Super admin accounts cannot be self-registered', 403);\n  }\n  \n  // Create user\n  const userData = {\n    email: validatedData.email.toLowerCase(),\n    password: validatedData.password,\n    firstName: validatedData.firstName,\n    lastName: validatedData.lastName,\n    phone: validatedData.phone,\n    role: validatedData.role || UserRole.MEMBER,\n    gymId: validatedData.gymId,\n    dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : undefined,\n    gender: validatedData.gender,\n    address: validatedData.address,\n    status: UserStatus.PENDING // New registrations are pending by default\n  };\n  \n  const user = new User(userData);\n  \n  // Generate email verification token\n  const verificationToken = user.generateEmailVerificationToken();\n  \n  await user.save();\n  \n  // TODO: Send verification email\n  // await sendVerificationEmail(user.email, verificationToken);\n  \n  // Remove sensitive data from response\n  const userResponse = user.toObject();\n  delete userResponse.password;\n  delete userResponse.passwordResetToken;\n  delete userResponse.emailVerificationToken;\n  \n  return createSuccessResponse(\n    userResponse, \n    'User registered successfully. Please check your email for verification instructions.',\n    null,\n    201\n  );\n});\n\n/**\n * GET /api/auth/register - Get registration information\n * Returns available gyms for registration\n */\nexport const GET = withErrorHandling(async (request: NextRequest) => {\n  await connectToDatabase();\n  \n  // Get available gyms for registration\n  const gyms = await Gym.find({ isActive: true })\n    .select('name subdomain description address')\n    .sort({ name: 1 })\n    .lean();\n  \n  return createSuccessResponse({\n    gyms,\n    availableRoles: [\n      { value: UserRole.MEMBER, label: 'Member' },\n      { value: UserRole.GYM_STAFF, label: 'Gym Staff' },\n      { value: UserRole.GYM_ADMIN, label: 'Gym Administrator' },\n      { value: UserRole.GYM_OWNER, label: 'Gym Owner' }\n    ]\n  }, 'Registration information retrieved successfully');\n});\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;;;;AASO,MAAM,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IAC3C,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAEtB,MAAM,OAAO,MAAM,QAAQ,IAAI;IAE/B,wBAAwB;IACxB,MAAM,gBAAgB,sIAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC;IAEnD,+BAA+B;IAC/B,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QACtC,OAAO,cAAc,KAAK,CAAC,WAAW;IACxC;IAEA,IAAI,cAAc;QAChB,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,uCAAuC;IACpE;IAEA,qDAAqD;IACrD,IAAI,cAAc,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,IAAI,CAAC,cAAc,KAAK,EAAE;QACvE,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,0CAA0C;IACvE;IAEA,yCAAyC;IACzC,IAAI,cAAc,KAAK,EAAE;QACvB,MAAM,MAAM,MAAM,sHAAA,CAAA,UAAG,CAAC,QAAQ,CAAC,cAAc,KAAK;QAClD,IAAI,CAAC,KAAK;YACR,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,kBAAkB;QAC/C;IACF;IAEA,oFAAoF;IACpF,IAAI,cAAc,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,EAAE;QAC/C,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,kDAAkD;IAC/E;IAEA,cAAc;IACd,MAAM,WAAW;QACf,OAAO,cAAc,KAAK,CAAC,WAAW;QACtC,UAAU,cAAc,QAAQ;QAChC,WAAW,cAAc,SAAS;QAClC,UAAU,cAAc,QAAQ;QAChC,OAAO,cAAc,KAAK;QAC1B,MAAM,cAAc,IAAI,IAAI,uHAAA,CAAA,WAAQ,CAAC,MAAM;QAC3C,OAAO,cAAc,KAAK;QAC1B,aAAa,cAAc,WAAW,GAAG,IAAI,KAAK,cAAc,WAAW,IAAI;QAC/E,QAAQ,cAAc,MAAM;QAC5B,SAAS,cAAc,OAAO;QAC9B,QAAQ,uHAAA,CAAA,aAAU,CAAC,OAAO,CAAC,2CAA2C;IACxE;IAEA,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;IAEtB,oCAAoC;IACpC,MAAM,oBAAoB,KAAK,8BAA8B;IAE7D,MAAM,KAAK,IAAI;IAEf,gCAAgC;IAChC,8DAA8D;IAE9D,sCAAsC;IACtC,MAAM,eAAe,KAAK,QAAQ;IAClC,OAAO,aAAa,QAAQ;IAC5B,OAAO,aAAa,kBAAkB;IACtC,OAAO,aAAa,sBAAsB;IAE1C,OAAO,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EACzB,cACA,wFACA,MACA;AAEJ;AAMO,MAAM,MAAM,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IAC1C,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAEtB,sCAAsC;IACtC,MAAM,OAAO,MAAM,sHAAA,CAAA,UAAG,CAAC,IAAI,CAAC;QAAE,UAAU;IAAK,GAC1C,MAAM,CAAC,sCACP,IAAI,CAAC;QAAE,MAAM;IAAE,GACf,IAAI;IAEP,OAAO,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;QAC3B;QACA,gBAAgB;YACd;gBAAE,OAAO,uHAAA,CAAA,WAAQ,CAAC,MAAM;gBAAE,OAAO;YAAS;YAC1C;gBAAE,OAAO,uHAAA,CAAA,WAAQ,CAAC,SAAS;gBAAE,OAAO;YAAY;YAChD;gBAAE,OAAO,uHAAA,CAAA,WAAQ,CAAC,SAAS;gBAAE,OAAO;YAAoB;YACxD;gBAAE,OAAO,uHAAA,CAAA,WAAQ,CAAC,SAAS;gBAAE,OAAO;YAAY;SACjD;IACH,GAAG;AACL", "debugId": null}}]}
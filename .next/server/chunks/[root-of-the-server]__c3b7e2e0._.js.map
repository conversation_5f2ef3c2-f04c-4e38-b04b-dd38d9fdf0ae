{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/app/api/tenant/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Get tenant information from headers (set by middleware)\n    const tenantId = request.headers.get('x-tenant-id');\n    const tenantSubdomain = request.headers.get('x-tenant-subdomain');\n    const tenantName = request.headers.get('x-tenant-name');\n\n    if (!tenantId) {\n      return NextResponse.json(\n        { error: 'Tenant not found' },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json({\n      tenant: {\n        id: tenantId,\n        subdomain: tenantSubdomain,\n        name: tenantName,\n      },\n      timestamp: new Date().toISOString(),\n    });\n  } catch (error) {\n    console.error('Tenant API error:', error);\n    \n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,0DAA0D;QAC1D,MAAM,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC;QACrC,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC5C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,UAAU;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;gBACN,IAAI;gBACJ,WAAW;gBACX,MAAM;YACR;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
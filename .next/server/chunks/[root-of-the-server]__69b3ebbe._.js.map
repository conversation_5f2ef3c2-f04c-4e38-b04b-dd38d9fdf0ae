{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\ninterface ConnectionCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongooseCache: ConnectionCache | undefined;\n}\n\nlet cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};\n\nif (!global.mongooseCache) {\n  global.mongooseCache = cached;\n}\n\nexport async function connectToDatabase(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4,\n    };\n\n    const mongoUri = process.env.MONGODB_URI;\n    if (!mongoUri) {\n      throw new Error('MONGODB_URI environment variable is not defined');\n    }\n\n    cached.promise = mongoose.connect(mongoUri, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n    console.log('✅ Connected to MongoDB');\n    return cached.conn;\n  } catch (error) {\n    cached.promise = null;\n    console.error('❌ MongoDB connection error:', error);\n    throw error;\n  }\n}\n\nexport async function disconnectFromDatabase(): Promise<void> {\n  if (cached.conn) {\n    await cached.conn.disconnect();\n    cached.conn = null;\n    cached.promise = null;\n    console.log('🔌 Disconnected from MongoDB');\n  }\n}\n\n// Connection event handlers\nmongoose.connection.on('connected', () => {\n  console.log('🔗 Mongoose connected to MongoDB');\n});\n\nmongoose.connection.on('error', (err) => {\n  console.error('❌ Mongoose connection error:', err);\n});\n\nmongoose.connection.on('disconnected', () => {\n  console.log('🔌 Mongoose disconnected from MongoDB');\n});\n\n// Graceful shutdown\nprocess.on('SIGINT', async () => {\n  await disconnectFromDatabase();\n  process.exit(0);\n});\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,IAAI,SAA0B,OAAO,aAAa,IAAI;IACpD,MAAM;IACN,SAAS;AACX;AAEA,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG;AACzB;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;IAC9C;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;AACF;AAEA,4BAA4B;AAC5B,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa;IAClC,QAAQ,GAAG,CAAC;AACd;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;IAC/B,QAAQ,KAAK,CAAC,gCAAgC;AAChD;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB;IACrC,QAAQ,GAAG,CAAC;AACd;AAEA,oBAAoB;AACpB,QAAQ,EAAE,CAAC,UAAU;IACnB,MAAM;IACN,QAAQ,IAAI,CAAC;AACf", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\n// User roles enum\nexport enum UserRole {\n  SUPER_ADMIN = 'super_admin',\n  GYM_OWNER = 'gym_owner',\n  GYM_ADMIN = 'gym_admin',\n  GYM_STAFF = 'gym_staff',\n  MEMBER = 'member'\n}\n\n// User status enum\nexport enum UserStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  SUSPENDED = 'suspended',\n  PENDING = 'pending'\n}\n\n// User interface\nexport interface IUser extends Document {\n  // Basic Information\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  avatar?: string;\n  \n  // Role and Permissions\n  role: UserRole;\n  status: UserStatus;\n  \n  // Multi-tenant Association\n  gymId?: string; // Optional for super_admin, required for others\n  gymIds?: string[]; // For users who can access multiple gyms (super_admin, gym_owner)\n  \n  // Authentication\n  emailVerified?: Date;\n  emailVerificationToken?: string;\n  passwordResetToken?: string;\n  passwordResetExpires?: Date;\n  lastLogin?: Date;\n  loginAttempts?: number;\n  lockUntil?: Date;\n  \n  // Profile Information\n  dateOfBirth?: Date;\n  gender?: 'male' | 'female' | 'other';\n  address?: {\n    street?: string;\n    city?: string;\n    state?: string;\n    zipCode?: string;\n    country?: string;\n  };\n  \n  // Preferences\n  preferences?: {\n    notifications?: {\n      email?: boolean;\n      sms?: boolean;\n      whatsapp?: boolean;\n    };\n    language?: string;\n    timezone?: string;\n  };\n  \n  // Timestamps\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Virtual properties\n  fullName: string;\n  isLocked: boolean;\n  \n  // Methods\n  comparePassword(candidatePassword: string): Promise<boolean>;\n  generatePasswordResetToken(): string;\n  generateEmailVerificationToken(): string;\n  incrementLoginAttempts(): Promise<void>;\n  resetLoginAttempts(): Promise<void>;\n}\n\n// User schema\nconst UserSchema = new Schema<IUser>({\n  // Basic Information\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [8, 'Password must be at least 8 characters long'],\n    select: false // Don't include password in queries by default\n  },\n  firstName: {\n    type: String,\n    required: [true, 'First name is required'],\n    trim: true,\n    maxlength: [50, 'First name cannot exceed 50 characters']\n  },\n  lastName: {\n    type: String,\n    required: [true, 'Last name is required'],\n    trim: true,\n    maxlength: [50, 'Last name cannot exceed 50 characters']\n  },\n  phone: {\n    type: String,\n    trim: true,\n    match: [/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Please enter a valid phone number']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  \n  // Role and Permissions\n  role: {\n    type: String,\n    enum: Object.values(UserRole),\n    required: [true, 'User role is required'],\n    default: UserRole.MEMBER\n  },\n  status: {\n    type: String,\n    enum: Object.values(UserStatus),\n    default: UserStatus.PENDING\n  },\n  \n  // Multi-tenant Association\n  gymId: {\n    type: String,\n    ref: 'Gym',\n    required: function(this: IUser) {\n      // gymId is required for all roles except super_admin\n      return this.role !== UserRole.SUPER_ADMIN;\n    },\n    index: true\n  },\n  gymIds: [{\n    type: String,\n    ref: 'Gym'\n  }],\n  \n  // Authentication\n  emailVerified: {\n    type: Date\n  },\n  emailVerificationToken: {\n    type: String,\n    select: false\n  },\n  passwordResetToken: {\n    type: String,\n    select: false\n  },\n  passwordResetExpires: {\n    type: Date,\n    select: false\n  },\n  lastLogin: {\n    type: Date\n  },\n  loginAttempts: {\n    type: Number,\n    default: 0\n  },\n  lockUntil: {\n    type: Date\n  },\n  \n  // Profile Information\n  dateOfBirth: {\n    type: Date\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other']\n  },\n  address: {\n    street: { type: String, trim: true },\n    city: { type: String, trim: true },\n    state: { type: String, trim: true },\n    zipCode: { type: String, trim: true },\n    country: { type: String, trim: true, default: 'US' }\n  },\n  \n  // Preferences\n  preferences: {\n    notifications: {\n      email: { type: Boolean, default: true },\n      sms: { type: Boolean, default: false },\n      whatsapp: { type: Boolean, default: false }\n    },\n    language: { type: String, default: 'en' },\n    timezone: { type: String, default: 'UTC' }\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for performance\nUserSchema.index({ email: 1 });\nUserSchema.index({ gymId: 1, role: 1 });\nUserSchema.index({ status: 1 });\nUserSchema.index({ createdAt: -1 });\n\n// Virtual properties\nUserSchema.virtual('fullName').get(function(this: IUser) {\n  return `${this.firstName} ${this.lastName}`.trim();\n});\n\nUserSchema.virtual('isLocked').get(function(this: IUser) {\n  return !!(this.lockUntil && this.lockUntil > new Date());\n});\n\n// Pre-save middleware\nUserSchema.pre('save', async function(this: IUser, next) {\n  // Only hash password if it's modified\n  if (!this.isModified('password')) return next();\n  \n  try {\n    // Hash password with cost of 12\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Pre-save middleware for gym association\nUserSchema.pre('save', function(this: IUser, next) {\n  // For super_admin, ensure gymIds array exists\n  if (this.role === UserRole.SUPER_ADMIN && !this.gymIds) {\n    this.gymIds = [];\n  }\n  \n  // For gym_owner, add gymId to gymIds array if not already present\n  if (this.role === UserRole.GYM_OWNER && this.gymId) {\n    if (!this.gymIds) this.gymIds = [];\n    if (!this.gymIds.includes(this.gymId)) {\n      this.gymIds.push(this.gymId);\n    }\n  }\n  \n  next();\n});\n\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  if (!this.password) return false;\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\nUserSchema.methods.generatePasswordResetToken = function(): string {\n  const resetToken = Math.random().toString(36).substring(2, 15) + \n                    Math.random().toString(36).substring(2, 15);\n  \n  this.passwordResetToken = resetToken;\n  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\n  \n  return resetToken;\n};\n\nUserSchema.methods.generateEmailVerificationToken = function(): string {\n  const verificationToken = Math.random().toString(36).substring(2, 15) + \n                           Math.random().toString(36).substring(2, 15);\n  \n  this.emailVerificationToken = verificationToken;\n  \n  return verificationToken;\n};\n\nUserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {\n  // If we have a previous lock that has expired, restart at 1\n  if (this.lockUntil && this.lockUntil < new Date()) {\n    return this.updateOne({\n      $unset: { lockUntil: 1 },\n      $set: { loginAttempts: 1 }\n    });\n  }\n  \n  const updates: any = { $inc: { loginAttempts: 1 } };\n  \n  // Lock account after 5 failed attempts for 2 hours\n  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {\n    updates.$set = { lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000) };\n  }\n  \n  return this.updateOne(updates);\n};\n\nUserSchema.methods.resetLoginAttempts = async function(): Promise<void> {\n  return this.updateOne({\n    $unset: { loginAttempts: 1, lockUntil: 1 },\n    $set: { lastLogin: new Date() }\n  });\n};\n\n// Export the model\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,IAAA,AAAK,kCAAA;;;;;;WAAA;;AASL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAwEZ,cAAc;AACd,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,oBAAoB;IACpB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAA8C;QAC7D,QAAQ,MAAM,+CAA+C;IAC/D;IACA,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAI;SAAwC;IAC1D;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAsB;SAAoC;IACpE;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IAEA,uBAAuB;IACvB,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC;QACpB,UAAU;YAAC;YAAM;SAAwB;QACzC,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,MAAM,OAAO,MAAM,CAAC;QACpB,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAO;QACL,MAAM;QACN,KAAK;QACL,UAAU;YACR,qDAAqD;YACrD,OAAO,IAAI,CAAC,IAAI;QAClB;QACA,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,KAAK;QACP;KAAE;IAEF,iBAAiB;IACjB,eAAe;QACb,MAAM;IACR;IACA,wBAAwB;QACtB,MAAM;QACN,QAAQ;IACV;IACA,oBAAoB;QAClB,MAAM;QACN,QAAQ;IACV;IACA,sBAAsB;QACpB,MAAM;QACN,QAAQ;IACV;IACA,WAAW;QACT,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;IAEA,sBAAsB;IACtB,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;SAAQ;IACnC;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,MAAM;QAAK;QACnC,MAAM;YAAE,MAAM;YAAQ,MAAM;QAAK;QACjC,OAAO;YAAE,MAAM;YAAQ,MAAM;QAAK;QAClC,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;QACpC,SAAS;YAAE,MAAM;YAAQ,MAAM;YAAM,SAAS;QAAK;IACrD;IAEA,cAAc;IACd,aAAa;QACX,eAAe;YACb,OAAO;gBAAE,MAAM;gBAAS,SAAS;YAAK;YACtC,KAAK;gBAAE,MAAM;gBAAS,SAAS;YAAM;YACrC,UAAU;gBAAE,MAAM;gBAAS,SAAS;YAAM;QAC5C;QACA,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;IAC3C;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,0BAA0B;AAC1B,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,OAAO;IAAG,MAAM;AAAE;AACrC,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AAEjC,qBAAqB;AACrB,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI;AAClD;AAEA,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM;AACzD;AAEA,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,eAA4B,IAAI;IACrD,sCAAsC;IACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,gCAAgC;QAChC,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0CAA0C;AAC1C,WAAW,GAAG,CAAC,QAAQ,SAAsB,IAAI;IAC/C,8CAA8C;IAC9C,IAAI,IAAI,CAAC,IAAI,sBAA6B,CAAC,IAAI,CAAC,MAAM,EAAE;QACtD,IAAI,CAAC,MAAM,GAAG,EAAE;IAClB;IAEA,kEAAkE;IAClE,IAAI,IAAI,CAAC,IAAI,oBAA2B,IAAI,CAAC,KAAK,EAAE;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QAC7B;IACF;IAEA;AACF;AAEA,mBAAmB;AACnB,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;IAC3B,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACzC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAE1D,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,aAAa;IAEhF,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM,oBAAoB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACzC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAEjE,IAAI,CAAC,sBAAsB,GAAG;IAE9B,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,sBAAsB,GAAG;IAC1C,4DAA4D;IAC5D,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ;gBAAE,WAAW;YAAE;YACvB,MAAM;gBAAE,eAAe;YAAE;QAC3B;IACF;IAEA,MAAM,UAAe;QAAE,MAAM;YAAE,eAAe;QAAE;IAAE;IAElD,mDAAmD;IACnD,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD,QAAQ,IAAI,GAAG;YAAE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK;QAAM;IACxE;IAEA,OAAO,IAAI,CAAC,SAAS,CAAC;AACxB;AAEA,WAAW,OAAO,CAAC,kBAAkB,GAAG;IACtC,OAAO,IAAI,CAAC,SAAS,CAAC;QACpB,QAAQ;YAAE,eAAe;YAAG,WAAW;QAAE;QACzC,MAAM;YAAE,WAAW,IAAI;QAAO;IAChC;AACF;uCAGe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/validations/schemas.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// Common schemas\nexport const emailSchema = z.string().email('Invalid email address');\nexport const phoneSchema = z.string().min(10, 'Phone number must be at least 10 digits');\nexport const passwordSchema = z.string().min(8, 'Password must be at least 8 characters');\n\n// Gym schema\nexport const gymSchema = z.object({\n  name: z.string().min(1, 'Gym name is required').max(100, 'Gym name too long'),\n  subdomain: z.string()\n    .min(3, 'Subdomain must be at least 3 characters')\n    .max(50, 'Subdomain too long')\n    .regex(/^[a-z0-9-]+$/, 'Subdomain can only contain lowercase letters, numbers, and hyphens'),\n  description: z.string().max(500, 'Description too long').optional(),\n  address: z.object({\n    street: z.string().min(1, 'Street address is required'),\n    city: z.string().min(1, 'City is required'),\n    state: z.string().min(1, 'State is required'),\n    zipCode: z.string().min(1, 'ZIP code is required'),\n    country: z.string().default('US'),\n  }),\n  contact: z.object({\n    phone: phoneSchema,\n    email: emailSchema,\n    website: z.string().url('Invalid website URL').optional(),\n  }),\n});\n\n// Package schema\nexport const packageSchema = z.object({\n  gymId: z.string().min(1, 'Gym ID is required'),\n  name: z.string().min(1, 'Package name is required').max(100, 'Package name too long'),\n  description: z.string().max(500, 'Description too long').optional(),\n  type: z.enum(['monthly', 'quarterly', 'yearly', 'custom']),\n  duration: z.object({\n    value: z.number().min(1, 'Duration value must be at least 1'),\n    unit: z.enum(['days', 'weeks', 'months', 'years']),\n  }),\n  pricing: z.object({\n    amount: z.number().min(0, 'Amount must be non-negative'),\n    currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),\n    setupFee: z.number().min(0, 'Setup fee must be non-negative').optional(),\n  }),\n  features: z.array(z.string()).default([]),\n  benefits: z.array(z.string()).default([]),\n  restrictions: z.object({\n    maxClasses: z.number().min(0).optional(),\n    maxTrainerSessions: z.number().min(0).optional(),\n    accessHours: z.object({\n      start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n      end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),\n    }).optional(),\n    allowedDays: z.array(z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])).optional(),\n  }).optional(),\n  isActive: z.boolean().default(true),\n  isPopular: z.boolean().default(false),\n  sortOrder: z.number().default(0),\n});\n\n// Member schema\nexport const memberSchema = z.object({\n  personalInfo: z.object({\n    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),\n    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),\n    email: emailSchema,\n    phone: phoneSchema,\n    dateOfBirth: z.string().transform((str) => new Date(str)).optional(),\n    gender: z.enum(['male', 'female', 'other']).optional(),\n    profilePhoto: z.string().url('Invalid photo URL').optional(),\n  }),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().default('US'),\n  }).optional(),\n  emergencyContact: z.object({\n    name: z.string().min(1, 'Emergency contact name is required'),\n    relationship: z.string().min(1, 'Relationship is required'),\n    phone: phoneSchema,\n  }),\n  healthInfo: z.object({\n    medicalConditions: z.array(z.string()).default([]),\n    allergies: z.array(z.string()).default([]),\n    medications: z.array(z.string()).default([]),\n    fitnessGoals: z.array(z.string()).default([]),\n    notes: z.string().max(1000, 'Notes too long').optional(),\n  }).optional(),\n  preferences: z.object({\n    preferredTrainers: z.array(z.string()).default([]),\n    notifications: z.object({\n      email: z.boolean().default(true),\n      sms: z.boolean().default(false),\n      whatsapp: z.boolean().default(false),\n    }),\n    language: z.string().default('en'),\n  }).optional(),\n});\n\n// Trainer schema\nexport const trainerSchema = z.object({\n  personalInfo: z.object({\n    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),\n    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),\n    email: emailSchema,\n    phone: phoneSchema,\n    dateOfBirth: z.string().transform((str) => new Date(str)).optional(),\n    gender: z.enum(['male', 'female', 'other']).optional(),\n    profilePhoto: z.string().url('Invalid photo URL').optional(),\n  }),\n  professional: z.object({\n    specializations: z.array(z.string()).default([]),\n    certifications: z.array(z.object({\n      name: z.string().min(1, 'Certification name is required'),\n      issuedBy: z.string().min(1, 'Issuing organization is required'),\n      issuedDate: z.string().transform((str) => new Date(str)),\n      expiryDate: z.string().transform((str) => new Date(str)).optional(),\n      certificateUrl: z.string().url('Invalid certificate URL').optional(),\n    })).default([]),\n    experience: z.number().min(0, 'Experience must be non-negative').default(0),\n    bio: z.string().max(1000, 'Bio too long').optional(),\n    hourlyRate: z.number().min(0, 'Hourly rate must be non-negative').optional(),\n  }),\n  employment: z.object({\n    hireDate: z.string().transform((str) => new Date(str)).default(() => new Date()),\n    employmentType: z.enum(['full-time', 'part-time', 'contract', 'freelance']),\n    salary: z.number().min(0, 'Salary must be non-negative').optional(),\n  }),\n});\n\n// Subscription schema\nexport const subscriptionSchema = z.object({\n  memberId: z.string().min(1, 'Member ID is required'),\n  packageId: z.string().min(1, 'Package ID is required'),\n  startDate: z.string().transform((str) => new Date(str)),\n  payment: z.object({\n    amount: z.number().min(0, 'Amount must be non-negative'),\n    currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),\n    setupFee: z.number().min(0, 'Setup fee must be non-negative').optional(),\n    paymentMethod: z.enum(['cash', 'card', 'bank_transfer', 'online', 'other']),\n    transactionId: z.string().optional(),\n  }),\n  autoRenewal: z.object({\n    enabled: z.boolean().default(false),\n  }).optional(),\n  notes: z.string().max(500, 'Notes too long').optional(),\n});\n\n// API query schemas\nexport const paginationSchema = z.object({\n  page: z.string().transform((val) => parseInt(val, 10)).default('1'),\n  limit: z.string().transform((val) => parseInt(val, 10)).default('10'),\n  search: z.string().optional(),\n  sortBy: z.string().optional(),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n});\n\nexport const idParamSchema = z.object({\n  id: z.string().min(1, 'ID is required'),\n});\n\n// User role enum\nexport const userRoleSchema = z.enum(['super_admin', 'gym_owner', 'gym_admin', 'gym_staff', 'member']);\n\n// User status enum\nexport const userStatusSchema = z.enum(['active', 'inactive', 'suspended', 'pending']);\n\n// User registration schema\nexport const userRegistrationSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n  confirmPassword: z.string(),\n  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),\n  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),\n  phone: z.string().regex(/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Invalid phone number').optional(),\n  role: userRoleSchema.default('member'),\n  gymId: z.string().optional(),\n  dateOfBirth: z.string().datetime().optional(),\n  gender: z.enum(['male', 'female', 'other']).optional(),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().default('US')\n  }).optional()\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n});\n\n// User login schema\nexport const userLoginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n  gymSubdomain: z.string().optional(),\n  rememberMe: z.boolean().default(false)\n});\n\n// User profile update schema\nexport const userProfileUpdateSchema = z.object({\n  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long').optional(),\n  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long').optional(),\n  phone: z.string().regex(/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Invalid phone number').optional(),\n  avatar: z.string().url('Invalid avatar URL').optional(),\n  dateOfBirth: z.string().datetime().optional(),\n  gender: z.enum(['male', 'female', 'other']).optional(),\n  address: z.object({\n    street: z.string().optional(),\n    city: z.string().optional(),\n    state: z.string().optional(),\n    zipCode: z.string().optional(),\n    country: z.string().optional()\n  }).optional(),\n  preferences: z.object({\n    notifications: z.object({\n      email: z.boolean().optional(),\n      sms: z.boolean().optional(),\n      whatsapp: z.boolean().optional()\n    }).optional(),\n    language: z.string().optional(),\n    timezone: z.string().optional()\n  }).optional()\n});\n\n// User admin update schema (for admin operations)\nexport const userAdminUpdateSchema = userProfileUpdateSchema.extend({\n  role: userRoleSchema.optional(),\n  status: userStatusSchema.optional(),\n  gymId: z.string().optional(),\n  gymIds: z.array(z.string()).optional()\n});\n\n// Password change schema\nexport const passwordChangeSchema = z.object({\n  currentPassword: z.string().min(1, 'Current password is required'),\n  newPassword: z.string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n  confirmNewPassword: z.string()\n}).refine((data) => data.newPassword === data.confirmNewPassword, {\n  message: \"New passwords don't match\",\n  path: [\"confirmNewPassword\"],\n});\n\n// Password reset request schema\nexport const passwordResetRequestSchema = z.object({\n  email: z.string().email('Invalid email address')\n});\n\n// Password reset schema\nexport const passwordResetSchema = z.object({\n  token: z.string().min(1, 'Reset token is required'),\n  password: z.string()\n    .min(8, 'Password must be at least 8 characters long')\n    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),\n  confirmPassword: z.string()\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n});\n\n// Email verification schema\nexport const emailVerificationSchema = z.object({\n  token: z.string().min(1, 'Verification token is required')\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;;AAGO,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AACrC,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;AACvC,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAGzC,MAAM,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,KAAK;IACzD,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAChB,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,IAAI,sBACR,KAAK,CAAC,gBAAgB;IACzB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;IACjE,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACzB,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC3B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B;IACA,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,OAAO;QACP,OAAO;QACP,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,uBAAuB,QAAQ;IACzD;AACF;AAGO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,KAAK;IAC7D,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;IACjE,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAa;QAAU;KAAS;IACzD,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACjB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAS;YAAU;SAAQ;IACnD;IACA,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,iCAAiC,OAAO,CAAC;QACxE,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,kCAAkC,QAAQ;IACxE;IACA,UAAU,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACxC,UAAU,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACxC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;QACtC,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;QAC9C,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACpB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;YAC7D,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,qCAAqC;QAC7D,GAAG,QAAQ;QACX,aAAa,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAU;YAAW;YAAa;YAAY;YAAU;YAAY;SAAS,GAAG,QAAQ;IACvH,GAAG,QAAQ;IACX,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,WAAW,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC;AAGO,MAAM,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI;QAC/D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI;QAC7D,OAAO;QACP,OAAO;QACP,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;QAClE,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAQ,EAAE,QAAQ;QACpD,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,qBAAqB,QAAQ;IAC5D;IACA,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B,GAAG,QAAQ;IACX,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACzB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QACxB,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAChC,OAAO;IACT;IACA,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,mBAAmB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QACjD,WAAW,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QACzC,aAAa,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC3C,cAAc,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC5C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,kBAAkB,QAAQ;IACxD,GAAG,QAAQ;IACX,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,mBAAmB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QACjD,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACtB,OAAO,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B,KAAK,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;YACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;QAChC;QACA,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC/B,GAAG,QAAQ;AACb;AAGO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI;QAC/D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI;QAC7D,OAAO;QACP,OAAO;QACP,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;QAClE,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAU;SAAQ,EAAE,QAAQ;QACpD,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,qBAAqB,QAAQ;IAC5D;IACA,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACrB,iBAAiB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;QAC/C,gBAAgB,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YAC/B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;YAC5B,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK;YACnD,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;YACjE,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,2BAA2B,QAAQ;QACpE,IAAI,OAAO,CAAC,EAAE;QACd,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,mCAAmC,OAAO,CAAC;QACzE,KAAK,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,gBAAgB,QAAQ;QAClD,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,oCAAoC,QAAQ;IAC5E;IACA,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACnB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,OAAO,CAAC,IAAM,IAAI;QACzE,gBAAgB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAa;YAAa;YAAY;SAAY;QAC1E,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,+BAA+B,QAAQ;IACnE;AACF;AAGO,MAAM,qBAAqB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK;IAClD,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC1B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,iCAAiC,OAAO,CAAC;QACxE,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,kCAAkC,QAAQ;QACtE,eAAe,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAQ;YAAQ;YAAiB;YAAU;SAAQ;QAC1E,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC;IACA,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,SAAS,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,GAAG,QAAQ;IACX,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,kBAAkB,QAAQ;AACvD;AAGO,MAAM,mBAAmB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,SAAS,KAAK,KAAK,OAAO,CAAC;IAC/D,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,SAAS,KAAK,KAAK,OAAO,CAAC;IAChE,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,WAAW,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;AAC7C;AAEO,MAAM,gBAAgB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACxB;AAGO,MAAM,iBAAiB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAe;IAAa;IAAa;IAAa;CAAS;AAG9F,MAAM,mBAAmB,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IAAC;IAAU;IAAY;IAAa;CAAU;AAG9E,MAAM,yBAAyB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,mCAAmC;IAC5C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM;IACzB,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI;IAC/D,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI;IAC7D,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,sBAAsB,wBAAwB,QAAQ;IAC9E,MAAM,eAAe,OAAO,CAAC;IAC7B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3C,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ,EAAE,QAAQ;IACpD,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,OAAO,CAAC;IAC9B,GAAG,QAAQ;AACb,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,kBAAkB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,YAAY,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAClC;AAGO,MAAM,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,WAAW,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,IAAI,uBAAuB,QAAQ;IAC9F,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,IAAI,sBAAsB,QAAQ;IAC3F,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,sBAAsB,wBAAwB,QAAQ;IAC9E,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,sBAAsB,QAAQ;IACrD,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3C,QAAQ,mLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ,EAAE,QAAQ;IACpD,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,QAAQ,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC1B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC5B,SAAS,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,GAAG,QAAQ;IACX,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACpB,eAAe,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;YACtB,OAAO,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YAC3B,KAAK,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;YACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;QAChC,GAAG,QAAQ;QACX,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,GAAG,QAAQ;AACb;AAGO,MAAM,wBAAwB,wBAAwB,MAAM,CAAC;IAClE,MAAM,eAAe,QAAQ;IAC7B,QAAQ,iBAAiB,QAAQ;IACjC,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,mLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AACtC;AAGO,MAAM,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,mCAAmC;IAC5C,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM;AAC9B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,kBAAkB,EAAE;IAChE,SAAS;IACT,MAAM;QAAC;KAAqB;AAC9B;AAGO,MAAM,6BAA6B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjD,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAGO,MAAM,sBAAsB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,GAAG,+CACP,KAAK,CAAC,mCAAmC;IAC5C,iBAAiB,mLAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGO,MAAM,0BAA0B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/utils/api.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { ZodError } from 'zod';\nimport { ApiResponse, PaginatedResponse } from '@/types';\n\n/**\n * Create a success response\n */\nexport function createSuccessResponse<T>(\n  data: T,\n  message?: string,\n  metadata?: any,\n  status: number = 200\n): NextResponse<ApiResponse<T>> {\n  return NextResponse.json(\n    {\n      success: true,\n      data,\n      message,\n      ...metadata\n    },\n    { status }\n  );\n}\n\n/**\n * Create an error response\n */\nexport function createErrorResponse(\n  error: string,\n  status: number = 400,\n  details?: any\n): NextResponse<ApiResponse> {\n  return NextResponse.json(\n    {\n      success: false,\n      error,\n      ...(details && { details }),\n    },\n    { status }\n  );\n}\n\n/**\n * Create a paginated response\n */\nexport function createPaginatedResponse<T>(\n  data: T[],\n  page: number,\n  limit: number,\n  total: number,\n  message?: string\n): NextResponse<PaginatedResponse<T>> {\n  const totalPages = Math.ceil(total / limit);\n\n  return NextResponse.json({\n    success: true,\n    data,\n    message,\n    pagination: {\n      page,\n      limit,\n      total,\n      totalPages,\n    },\n  });\n}\n\n/**\n * Handle validation errors from Zod\n */\nexport function handleValidationError(error: ZodError): NextResponse<ApiResponse> {\n  const formattedErrors = error.errors.map((err) => ({\n    field: err.path.join('.'),\n    message: err.message,\n  }));\n\n  return createErrorResponse(\n    'Validation failed',\n    400,\n    { validationErrors: formattedErrors }\n  );\n}\n\n/**\n * Handle database errors\n */\nexport function handleDatabaseError(error: any): NextResponse<ApiResponse> {\n  console.error('Database error:', error);\n\n  // Handle MongoDB duplicate key error\n  if (error.code === 11000) {\n    const field = Object.keys(error.keyPattern || {})[0] || 'field';\n    return createErrorResponse(`${field} already exists`, 409);\n  }\n\n  // Handle validation errors\n  if (error.name === 'ValidationError') {\n    const validationErrors = Object.values(error.errors).map((err: any) => ({\n      field: err.path,\n      message: err.message,\n    }));\n    return createErrorResponse('Validation failed', 400, { validationErrors });\n  }\n\n  // Handle cast errors (invalid ObjectId, etc.)\n  if (error.name === 'CastError') {\n    return createErrorResponse('Invalid ID format', 400);\n  }\n\n  // Generic database error\n  return createErrorResponse('Database operation failed', 500);\n}\n\n/**\n * Handle async route errors\n */\nexport function withErrorHandling(\n  handler: (request: Request, context?: any) => Promise<NextResponse>\n) {\n  return async (request: Request, context?: any): Promise<NextResponse> => {\n    try {\n      return await handler(request, context);\n    } catch (error) {\n      console.error('API route error:', error);\n\n      if (error instanceof ZodError) {\n        return handleValidationError(error);\n      }\n\n      if (error && typeof error === 'object' && 'code' in error) {\n        return handleDatabaseError(error);\n      }\n\n      return createErrorResponse(\n        error instanceof Error ? error.message : 'Internal server error',\n        500\n      );\n    }\n  };\n}\n\n/**\n * Extract pagination parameters from URL search params\n */\nexport function extractPaginationParams(searchParams: URLSearchParams) {\n  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));\n  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10', 10)));\n  const search = searchParams.get('search') || undefined;\n  const sortBy = searchParams.get('sortBy') || 'createdAt';\n  const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';\n\n  return {\n    page,\n    limit,\n    skip: (page - 1) * limit,\n    search,\n    sortBy,\n    sortOrder,\n  };\n}\n\n/**\n * Build MongoDB sort object\n */\nexport function buildSortObject(sortBy: string, sortOrder: 'asc' | 'desc') {\n  return { [sortBy]: sortOrder === 'asc' ? 1 : -1 };\n}\n\n/**\n * Build MongoDB search filter\n */\nexport function buildSearchFilter(search: string | undefined, fields: string[]) {\n  if (!search) return {};\n\n  const searchRegex = new RegExp(search, 'i');\n  return {\n    $or: fields.map((field) => ({ [field]: searchRegex })),\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AAAA;;;AAMO,SAAS,sBACd,IAAO,EACP,OAAgB,EAChB,QAAc,EACd,SAAiB,GAAG;IAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT;QACA;QACA,GAAG,QAAQ;IACb,GACA;QAAE;IAAO;AAEb;AAKO,SAAS,oBACd,KAAa,EACb,SAAiB,GAAG,EACpB,OAAa;IAEb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QACE,SAAS;QACT;QACA,GAAI,WAAW;YAAE;QAAQ,CAAC;IAC5B,GACA;QAAE;IAAO;AAEb;AAKO,SAAS,wBACd,IAAS,EACT,IAAY,EACZ,KAAa,EACb,KAAa,EACb,OAAgB;IAEhB,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT;QACA;QACA,YAAY;YACV;YACA;YACA;YACA;QACF;IACF;AACF;AAKO,SAAS,sBAAsB,KAAe;IACnD,MAAM,kBAAkB,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,MAAQ,CAAC;YACjD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC;YACrB,SAAS,IAAI,OAAO;QACtB,CAAC;IAED,OAAO,oBACL,qBACA,KACA;QAAE,kBAAkB;IAAgB;AAExC;AAKO,SAAS,oBAAoB,KAAU;IAC5C,QAAQ,KAAK,CAAC,mBAAmB;IAEjC,qCAAqC;IACrC,IAAI,MAAM,IAAI,KAAK,OAAO;QACxB,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI;QACxD,OAAO,oBAAoB,GAAG,MAAM,eAAe,CAAC,EAAE;IACxD;IAEA,2BAA2B;IAC3B,IAAI,MAAM,IAAI,KAAK,mBAAmB;QACpC,MAAM,mBAAmB,OAAO,MAAM,CAAC,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAa,CAAC;gBACtE,OAAO,IAAI,IAAI;gBACf,SAAS,IAAI,OAAO;YACtB,CAAC;QACD,OAAO,oBAAoB,qBAAqB,KAAK;YAAE;QAAiB;IAC1E;IAEA,8CAA8C;IAC9C,IAAI,MAAM,IAAI,KAAK,aAAa;QAC9B,OAAO,oBAAoB,qBAAqB;IAClD;IAEA,yBAAyB;IACzB,OAAO,oBAAoB,6BAA6B;AAC1D;AAKO,SAAS,kBACd,OAAmE;IAEnE,OAAO,OAAO,SAAkB;QAC9B,IAAI;YACF,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAElC,IAAI,iBAAiB,sJAAA,CAAA,WAAQ,EAAE;gBAC7B,OAAO,sBAAsB;YAC/B;YAEA,IAAI,SAAS,OAAO,UAAU,YAAY,UAAU,OAAO;gBACzD,OAAO,oBAAoB;YAC7B;YAEA,OAAO,oBACL,iBAAiB,QAAQ,MAAM,OAAO,GAAG,yBACzC;QAEJ;IACF;AACF;AAKO,SAAS,wBAAwB,YAA6B;IACnE,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,WAAW,KAAK;IACnE,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,YAAY,MAAM;IACpF,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;IAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;IAC7C,MAAM,YAAa,aAAa,GAAG,CAAC,gBAAgB;IAEpD,OAAO;QACL;QACA;QACA,MAAM,CAAC,OAAO,CAAC,IAAI;QACnB;QACA;QACA;IACF;AACF;AAKO,SAAS,gBAAgB,MAAc,EAAE,SAAyB;IACvE,OAAO;QAAE,CAAC,OAAO,EAAE,cAAc,QAAQ,IAAI,CAAC;IAAE;AAClD;AAKO,SAAS,kBAAkB,MAA0B,EAAE,MAAgB;IAC5E,IAAI,CAAC,QAAQ,OAAO,CAAC;IAErB,MAAM,cAAc,IAAI,OAAO,QAAQ;IACvC,OAAO;QACL,KAAK,OAAO,GAAG,CAAC,CAAC,QAAU,CAAC;gBAAE,CAAC,MAAM,EAAE;YAAY,CAAC;IACtD;AACF", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/auth/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getToken } from 'next-auth/jwt';\nimport { UserRole, UserStatus } from '@/models/User';\nimport { createErrorResponse } from '@/lib/utils/api';\n\n// Authentication middleware options\ninterface AuthMiddlewareOptions {\n  requireAuth?: boolean;\n  roles?: UserRole[];\n  minimumRole?: UserRole;\n  requireGymAccess?: boolean;\n  allowSuperAdmin?: boolean;\n}\n\n// Enhanced request with user information\nexport interface AuthenticatedRequest extends NextRequest {\n  user?: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    role: UserRole;\n    status: UserStatus;\n    gymId?: string;\n    gymIds?: string[];\n  };\n  gym?: {\n    id: string;\n    name: string;\n    subdomain: string;\n  };\n}\n\n// Role hierarchy for permission checking\nconst ROLE_HIERARCHY = {\n  [UserRole.SUPER_ADMIN]: 5,\n  [UserRole.GYM_OWNER]: 4,\n  [UserRole.GYM_ADMIN]: 3,\n  [UserRole.GYM_STAFF]: 2,\n  [UserRole.MEMBER]: 1\n};\n\n// Authentication middleware\nexport function withAuth(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>,\n  options: AuthMiddlewareOptions = {}\n) {\n  return async (request: NextRequest, context?: any): Promise<NextResponse> => {\n    try {\n      // Get JWT token from request\n      const token = await getToken({ \n        req: request, \n        secret: process.env.NEXTAUTH_SECRET \n      });\n\n      // Check if authentication is required\n      if (options.requireAuth !== false) {\n        if (!token) {\n          return createErrorResponse('Authentication required', 401);\n        }\n\n        // Check user status\n        if (token.status !== UserStatus.ACTIVE) {\n          return createErrorResponse('Account is not active', 403);\n        }\n      }\n\n      // Attach user information to request if authenticated\n      const authenticatedRequest = request as AuthenticatedRequest;\n      if (token) {\n        authenticatedRequest.user = {\n          id: token.id,\n          email: token.email || '',\n          firstName: token.firstName || '',\n          lastName: token.lastName || '',\n          role: token.role,\n          status: token.status,\n          gymId: token.gymId,\n          gymIds: token.gymIds\n        };\n      }\n\n      // Role-based authorization\n      if (token && options.roles && options.roles.length > 0) {\n        if (!options.roles.includes(token.role)) {\n          return createErrorResponse('Insufficient permissions', 403);\n        }\n      }\n\n      // Minimum role authorization\n      if (token && options.minimumRole) {\n        const userLevel = ROLE_HIERARCHY[token.role] || 0;\n        const minimumLevel = ROLE_HIERARCHY[options.minimumRole] || 0;\n        \n        if (userLevel < minimumLevel) {\n          return createErrorResponse('Insufficient role level', 403);\n        }\n      }\n\n      // Gym access authorization\n      if (token && options.requireGymAccess) {\n        const gymId = getGymIdFromRequest(request);\n        \n        if (gymId && !hasGymAccess(token, gymId)) {\n          return createErrorResponse('Access to this gym is not authorized', 403);\n        }\n      }\n\n      // Call the original handler\n      return await handler(authenticatedRequest, context);\n\n    } catch (error) {\n      console.error('Authentication middleware error:', error);\n      return createErrorResponse('Authentication error', 500);\n    }\n  };\n}\n\n// Helper function to check gym access\nfunction hasGymAccess(token: any, gymId: string): boolean {\n  // Super admin has access to all gyms\n  if (token.role === UserRole.SUPER_ADMIN) return true;\n  \n  // Check if user's primary gym matches\n  if (token.gymId === gymId) return true;\n  \n  // Check if user has access to multiple gyms\n  if (token.gymIds && token.gymIds.includes(gymId)) return true;\n  \n  return false;\n}\n\n// Helper function to extract gym ID from request\nfunction getGymIdFromRequest(request: NextRequest): string | null {\n  // Try to get gymId from query parameters\n  const { searchParams } = new URL(request.url);\n  const gymIdFromQuery = searchParams.get('gymId');\n  if (gymIdFromQuery) return gymIdFromQuery;\n  \n  // Try to get gymId from headers (set by tenant middleware)\n  const gymIdFromHeader = request.headers.get('x-gym-id');\n  if (gymIdFromHeader) return gymIdFromHeader;\n  \n  return null;\n}\n\n// Specific middleware functions for common use cases\n\n// Require authentication\nexport function requireAuth(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, { requireAuth: true });\n}\n\n// Require admin privileges\nexport function requireAdmin(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN]\n  });\n}\n\n// Require staff privileges\nexport function requireStaff(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN, UserRole.GYM_STAFF]\n  });\n}\n\n// Require super admin\nexport function requireSuperAdmin(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    roles: [UserRole.SUPER_ADMIN]\n  });\n}\n\n// Require gym owner\nexport function requireGymOwner(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER]\n  });\n}\n\n// Require gym access\nexport function requireGymAccess(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    requireGymAccess: true\n  });\n}\n\n// Combine authentication with gym access\nexport function requireAuthAndGymAccess(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    requireGymAccess: true\n  });\n}\n\n// Admin with gym access\nexport function requireAdminAndGymAccess(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN],\n    requireGymAccess: true\n  });\n}\n\n// Staff with gym access\nexport function requireStaffAndGymAccess(\n  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>\n) {\n  return withAuth(handler, {\n    requireAuth: true,\n    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN, UserRole.GYM_STAFF],\n    requireGymAccess: true\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;AACA;;;;AA8BA,yCAAyC;AACzC,MAAM,iBAAiB;IACrB,CAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,EAAE;IACxB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;IACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;IACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;IACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,EAAE;AACrB;AAGO,SAAS,SACd,OAAgF,EAChF,UAAiC,CAAC,CAAC;IAEnC,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,6BAA6B;YAC7B,MAAM,QAAQ,MAAM,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE;gBAC3B,KAAK;gBACL,QAAQ,QAAQ,GAAG,CAAC,eAAe;YACrC;YAEA,sCAAsC;YACtC,IAAI,QAAQ,WAAW,KAAK,OAAO;gBACjC,IAAI,CAAC,OAAO;oBACV,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,2BAA2B;gBACxD;gBAEA,oBAAoB;gBACpB,IAAI,MAAM,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,MAAM,EAAE;oBACtC,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,yBAAyB;gBACtD;YACF;YAEA,sDAAsD;YACtD,MAAM,uBAAuB;YAC7B,IAAI,OAAO;gBACT,qBAAqB,IAAI,GAAG;oBAC1B,IAAI,MAAM,EAAE;oBACZ,OAAO,MAAM,KAAK,IAAI;oBACtB,WAAW,MAAM,SAAS,IAAI;oBAC9B,UAAU,MAAM,QAAQ,IAAI;oBAC5B,MAAM,MAAM,IAAI;oBAChB,QAAQ,MAAM,MAAM;oBACpB,OAAO,MAAM,KAAK;oBAClB,QAAQ,MAAM,MAAM;gBACtB;YACF;YAEA,2BAA2B;YAC3B,IAAI,SAAS,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;gBACtD,IAAI,CAAC,QAAQ,KAAK,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG;oBACvC,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,4BAA4B;gBACzD;YACF;YAEA,6BAA6B;YAC7B,IAAI,SAAS,QAAQ,WAAW,EAAE;gBAChC,MAAM,YAAY,cAAc,CAAC,MAAM,IAAI,CAAC,IAAI;gBAChD,MAAM,eAAe,cAAc,CAAC,QAAQ,WAAW,CAAC,IAAI;gBAE5D,IAAI,YAAY,cAAc;oBAC5B,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,2BAA2B;gBACxD;YACF;YAEA,2BAA2B;YAC3B,IAAI,SAAS,QAAQ,gBAAgB,EAAE;gBACrC,MAAM,QAAQ,oBAAoB;gBAElC,IAAI,SAAS,CAAC,aAAa,OAAO,QAAQ;oBACxC,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,wCAAwC;gBACrE;YACF;YAEA,4BAA4B;YAC5B,OAAO,MAAM,QAAQ,sBAAsB;QAE7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,wBAAwB;QACrD;IACF;AACF;AAEA,sCAAsC;AACtC,SAAS,aAAa,KAAU,EAAE,KAAa;IAC7C,qCAAqC;IACrC,IAAI,MAAM,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,OAAO;IAEhD,sCAAsC;IACtC,IAAI,MAAM,KAAK,KAAK,OAAO,OAAO;IAElC,4CAA4C;IAC5C,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,OAAO;IAEzD,OAAO;AACT;AAEA,iDAAiD;AACjD,SAAS,oBAAoB,OAAoB;IAC/C,yCAAyC;IACzC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,iBAAiB,aAAa,GAAG,CAAC;IACxC,IAAI,gBAAgB,OAAO;IAE3B,2DAA2D;IAC3D,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC5C,IAAI,iBAAiB,OAAO;IAE5B,OAAO;AACT;AAKO,SAAS,YACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QAAE,aAAa;IAAK;AAC/C;AAGO,SAAS,aACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,OAAO;YAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IACvE;AACF;AAGO,SAAS,aACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,OAAO;YAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IAC3F;AACF;AAGO,SAAS,kBACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,OAAO;YAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;SAAC;IAC/B;AACF;AAGO,SAAS,gBACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,OAAO;YAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;IACnD;AACF;AAGO,SAAS,iBACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,kBAAkB;IACpB;AACF;AAGO,SAAS,wBACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,kBAAkB;IACpB;AACF;AAGO,SAAS,yBACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,OAAO;YAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;QACrE,kBAAkB;IACpB;AACF;AAGO,SAAS,yBACd,OAAgF;IAEhF,OAAO,SAAS,SAAS;QACvB,aAAa;QACb,OAAO;YAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;YAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;SAAC;QACzF,kBAAkB;IACpB;AACF", "debugId": null}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/Gym.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IGym extends Document {\n  _id: string;\n  name: string;\n  subdomain: string;\n  domain: string;\n  logo?: string;\n  description?: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    zipCode: string;\n    country: string;\n  };\n  contact: {\n    phone: string;\n    email: string;\n    website?: string;\n  };\n  settings: {\n    timezone: string;\n    currency: string;\n    language: string;\n    dateFormat: string;\n    businessHours: {\n      [key: string]: {\n        open: string;\n        close: string;\n        isOpen: boolean;\n      };\n    };\n  };\n  subscription: {\n    plan: 'free' | 'basic' | 'premium' | 'enterprise';\n    status: 'active' | 'inactive' | 'suspended' | 'cancelled';\n    startDate: Date;\n    endDate?: Date;\n    maxMembers: number;\n    maxTrainers: number;\n  };\n  whatsapp: {\n    enabled: boolean;\n    accountSid?: string;\n    authToken?: string;\n    phoneNumber?: string;\n  };\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst GymSchema = new Schema<IGym>({\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: 100,\n  },\n  subdomain: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: /^[a-z0-9-]+$/,\n    maxlength: 50,\n  },\n  domain: {\n    type: String,\n    trim: true,\n  },\n  logo: {\n    type: String,\n    trim: true,\n  },\n  description: {\n    type: String,\n    trim: true,\n    maxlength: 500,\n  },\n  address: {\n    street: { type: String, required: true, trim: true },\n    city: { type: String, required: true, trim: true },\n    state: { type: String, required: true, trim: true },\n    zipCode: { type: String, required: true, trim: true },\n    country: { type: String, required: true, trim: true, default: 'US' },\n  },\n  contact: {\n    phone: { type: String, required: true, trim: true },\n    email: { type: String, required: true, trim: true, lowercase: true },\n    website: { type: String, trim: true },\n  },\n  settings: {\n    timezone: { type: String, default: 'America/New_York' },\n    currency: { type: String, default: 'USD' },\n    language: { type: String, default: 'en' },\n    dateFormat: { type: String, default: 'MM/DD/YYYY' },\n    businessHours: {\n      type: Map,\n      of: {\n        open: { type: String, default: '06:00' },\n        close: { type: String, default: '22:00' },\n        isOpen: { type: Boolean, default: true },\n      },\n      default: {\n        monday: { open: '06:00', close: '22:00', isOpen: true },\n        tuesday: { open: '06:00', close: '22:00', isOpen: true },\n        wednesday: { open: '06:00', close: '22:00', isOpen: true },\n        thursday: { open: '06:00', close: '22:00', isOpen: true },\n        friday: { open: '06:00', close: '22:00', isOpen: true },\n        saturday: { open: '08:00', close: '20:00', isOpen: true },\n        sunday: { open: '08:00', close: '20:00', isOpen: true },\n      },\n    },\n  },\n  subscription: {\n    plan: {\n      type: String,\n      enum: ['free', 'basic', 'premium', 'enterprise'],\n      default: 'free',\n    },\n    status: {\n      type: String,\n      enum: ['active', 'inactive', 'suspended', 'cancelled'],\n      default: 'active',\n    },\n    startDate: { type: Date, default: Date.now },\n    endDate: Date,\n    maxMembers: { type: Number, default: 50 },\n    maxTrainers: { type: Number, default: 5 },\n  },\n  whatsapp: {\n    enabled: { type: Boolean, default: false },\n    accountSid: String,\n    authToken: String,\n    phoneNumber: String,\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true },\n});\n\n// Indexes\nGymSchema.index({ subdomain: 1 });\nGymSchema.index({ domain: 1 });\nGymSchema.index({ isActive: 1 });\nGymSchema.index({ 'subscription.status': 1 });\n\n// Virtual for full address\nGymSchema.virtual('fullAddress').get(function() {\n  return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;\n});\n\n// Pre-save middleware\nGymSchema.pre('save', function(next) {\n  if (this.isModified('subdomain') || this.isNew) {\n    this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;\n  }\n  next();\n});\n\nexport default mongoose.models.Gym || mongoose.model<IGym>('Gym', GymSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqDA,MAAM,YAAY,IAAI,yGAAA,CAAA,SAAM,CAAO;IACjC,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;IACb;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACnD,MAAM;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACjD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACpD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,SAAS;QAAK;IACrE;IACA,SAAS;QACP,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,WAAW;QAAK;QACnE,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;IACtC;IACA,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAmB;QACtD,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAa;QAClD,eAAe;YACb,MAAM;YACN,IAAI;gBACF,MAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACvC,OAAO;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACxC,QAAQ;oBAAE,MAAM;oBAAS,SAAS;gBAAK;YACzC;YACA,SAAS;gBACP,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACvD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACzD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;YACxD;QACF;IACF;IACA,cAAc;QACZ,MAAM;YACJ,MAAM;YACN,MAAM;gBAAC;gBAAQ;gBAAS;gBAAW;aAAa;YAChD,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,MAAM;gBAAC;gBAAU;gBAAY;gBAAa;aAAY;YACtD,SAAS;QACX;QACA,WAAW;YAAE,MAAM;YAAM,SAAS,KAAK,GAAG;QAAC;QAC3C,SAAS;QACT,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAG;QACxC,aAAa;YAAE,MAAM;YAAQ,SAAS;QAAE;IAC1C;IACA,UAAU;QACR,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,UAAU,KAAK,CAAC;IAAE,WAAW;AAAE;AAC/B,UAAU,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC5B,UAAU,KAAK,CAAC;IAAE,UAAU;AAAE;AAC9B,UAAU,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAE3C,2BAA2B;AAC3B,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;IACnC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC/H;AAEA,sBAAsB;AACtB,UAAU,GAAG,CAAC,QAAQ,SAAS,IAAI;IACjC,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,CAAC,KAAK,EAAE;QAC9C,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,UAAU,IAAI,kBAAkB;IACjF;IACA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAO,OAAO", "debugId": null}}, {"offset": {"line": 1436, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/auth/config.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { MongoDBAdapter } from '@auth/mongodb-adapter';\nimport { connectToDatabase } from '@/lib/database/connection';\nimport User, { UserRole, UserStatus } from '@/models/User';\nimport Gym from '@/models/Gym';\nimport { z } from 'zod';\n\n// Login schema validation\nconst loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n  gymSubdomain: z.string().optional()\n});\n\n// Extended user type for NextAuth\ndeclare module 'next-auth' {\n  interface User {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    role: UserRole;\n    status: UserStatus;\n    gymId?: string;\n    gymIds?: string[];\n    avatar?: string;\n  }\n\n  interface Session {\n    user: {\n      id: string;\n      email: string;\n      firstName: string;\n      lastName: string;\n      fullName: string;\n      role: UserRole;\n      status: UserStatus;\n      gymId?: string;\n      gymIds?: string[];\n      avatar?: string;\n    };\n    gym?: {\n      id: string;\n      name: string;\n      subdomain: string;\n      domain: string;\n    };\n  }\n}\n\ndeclare module 'next-auth/jwt' {\n  interface JWT {\n    id: string;\n    role: UserRole;\n    status: UserStatus;\n    gymId?: string;\n    gymIds?: string[];\n    gymSubdomain?: string;\n  }\n}\n\nexport const authOptions: NextAuthOptions = {\n  adapter: MongoDBAdapter(\n    connectToDatabase().then(({ db }) => db)\n  ),\n  \n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n        gymSubdomain: { label: 'Gym Subdomain', type: 'text' }\n      },\n      async authorize(credentials) {\n        try {\n          if (!credentials?.email || !credentials?.password) {\n            throw new Error('Email and password are required');\n          }\n\n          // Validate input\n          const validatedCredentials = loginSchema.parse(credentials);\n          \n          // Connect to database\n          await connectToDatabase();\n\n          // Find user by email\n          const user = await User.findOne({ \n            email: validatedCredentials.email.toLowerCase() \n          }).select('+password');\n\n          if (!user) {\n            throw new Error('Invalid email or password');\n          }\n\n          // Check if user is locked\n          if (user.isLocked) {\n            throw new Error('Account is temporarily locked due to too many failed login attempts');\n          }\n\n          // Check if user status allows login\n          if (user.status === UserStatus.SUSPENDED) {\n            throw new Error('Account is suspended');\n          }\n\n          if (user.status === UserStatus.INACTIVE) {\n            throw new Error('Account is inactive');\n          }\n\n          // Verify password\n          const isPasswordValid = await user.comparePassword(validatedCredentials.password);\n          \n          if (!isPasswordValid) {\n            // Increment login attempts\n            await user.incrementLoginAttempts();\n            throw new Error('Invalid email or password');\n          }\n\n          // Reset login attempts on successful login\n          await user.resetLoginAttempts();\n\n          // Handle gym-specific login\n          let selectedGymId = user.gymId;\n          \n          if (validatedCredentials.gymSubdomain) {\n            // Verify user has access to the specified gym\n            const gym = await Gym.findOne({ subdomain: validatedCredentials.gymSubdomain });\n            \n            if (!gym) {\n              throw new Error('Gym not found');\n            }\n\n            // Check if user has access to this gym\n            const hasAccess = user.role === UserRole.SUPER_ADMIN || \n                             user.gymId === gym._id.toString() ||\n                             (user.gymIds && user.gymIds.includes(gym._id.toString()));\n\n            if (!hasAccess) {\n              throw new Error('You do not have access to this gym');\n            }\n\n            selectedGymId = gym._id.toString();\n          }\n\n          // Return user object for NextAuth\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            role: user.role,\n            status: user.status,\n            gymId: selectedGymId,\n            gymIds: user.gymIds,\n            avatar: user.avatar\n          };\n\n        } catch (error) {\n          console.error('Authentication error:', error);\n          throw new Error(error instanceof Error ? error.message : 'Authentication failed');\n        }\n      }\n    })\n  ],\n\n  session: {\n    strategy: 'jwt',\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n\n  jwt: {\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n\n  callbacks: {\n    async jwt({ token, user, account }) {\n      // Initial sign in\n      if (account && user) {\n        token.id = user.id;\n        token.role = user.role;\n        token.status = user.status;\n        token.gymId = user.gymId;\n        token.gymIds = user.gymIds;\n      }\n\n      return token;\n    },\n\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id;\n        session.user.role = token.role;\n        session.user.status = token.status;\n        session.user.gymId = token.gymId;\n        session.user.gymIds = token.gymIds;\n        session.user.fullName = `${session.user.firstName} ${session.user.lastName}`;\n\n        // Add gym information to session if user has a selected gym\n        if (token.gymId) {\n          try {\n            await connectToDatabase();\n            const gym = await Gym.findById(token.gymId).select('name subdomain domain');\n            \n            if (gym) {\n              session.gym = {\n                id: gym._id.toString(),\n                name: gym.name,\n                subdomain: gym.subdomain,\n                domain: gym.domain\n              };\n            }\n          } catch (error) {\n            console.error('Error fetching gym for session:', error);\n          }\n        }\n      }\n\n      return session;\n    },\n\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      \n      // Allows callback URLs on the same origin\n      if (new URL(url).origin === baseUrl) return url;\n      \n      return baseUrl;\n    }\n  },\n\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n    newUser: '/auth/welcome'\n  },\n\n  events: {\n    async signIn({ user, account, profile, isNewUser }) {\n      console.log('User signed in:', { \n        userId: user.id, \n        email: user.email, \n        role: user.role,\n        gymId: user.gymId \n      });\n    },\n\n    async signOut({ session, token }) {\n      console.log('User signed out:', { \n        userId: token?.id || session?.user?.id \n      });\n    }\n  },\n\n  debug: process.env.NODE_ENV === 'development',\n};\n\nexport default authOptions;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEA,0BAA0B;AAC1B,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACnC;AAiDO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EACpB,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK;IAGvC,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;gBAChD,cAAc;oBAAE,OAAO;oBAAiB,MAAM;gBAAO;YACvD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI;oBACF,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;wBACjD,MAAM,IAAI,MAAM;oBAClB;oBAEA,iBAAiB;oBACjB,MAAM,uBAAuB,YAAY,KAAK,CAAC;oBAE/C,sBAAsB;oBACtB,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;oBAEtB,qBAAqB;oBACrB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,qBAAqB,KAAK,CAAC,WAAW;oBAC/C,GAAG,MAAM,CAAC;oBAEV,IAAI,CAAC,MAAM;wBACT,MAAM,IAAI,MAAM;oBAClB;oBAEA,0BAA0B;oBAC1B,IAAI,KAAK,QAAQ,EAAE;wBACjB,MAAM,IAAI,MAAM;oBAClB;oBAEA,oCAAoC;oBACpC,IAAI,KAAK,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,SAAS,EAAE;wBACxC,MAAM,IAAI,MAAM;oBAClB;oBAEA,IAAI,KAAK,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,QAAQ,EAAE;wBACvC,MAAM,IAAI,MAAM;oBAClB;oBAEA,kBAAkB;oBAClB,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,qBAAqB,QAAQ;oBAEhF,IAAI,CAAC,iBAAiB;wBACpB,2BAA2B;wBAC3B,MAAM,KAAK,sBAAsB;wBACjC,MAAM,IAAI,MAAM;oBAClB;oBAEA,2CAA2C;oBAC3C,MAAM,KAAK,kBAAkB;oBAE7B,4BAA4B;oBAC5B,IAAI,gBAAgB,KAAK,KAAK;oBAE9B,IAAI,qBAAqB,YAAY,EAAE;wBACrC,8CAA8C;wBAC9C,MAAM,MAAM,MAAM,sHAAA,CAAA,UAAG,CAAC,OAAO,CAAC;4BAAE,WAAW,qBAAqB,YAAY;wBAAC;wBAE7E,IAAI,CAAC,KAAK;4BACR,MAAM,IAAI,MAAM;wBAClB;wBAEA,uCAAuC;wBACvC,MAAM,YAAY,KAAK,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,IACnC,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,QAAQ,MAC9B,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ;wBAEtE,IAAI,CAAC,WAAW;4BACd,MAAM,IAAI,MAAM;wBAClB;wBAEA,gBAAgB,IAAI,GAAG,CAAC,QAAQ;oBAClC;oBAEA,kCAAkC;oBAClC,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,QAAQ,KAAK,MAAM;wBACnB,OAAO;wBACP,QAAQ,KAAK,MAAM;wBACnB,QAAQ,KAAK,MAAM;oBACrB;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3D;YACF;QACF;KACD;IAED,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IAEA,KAAK;QACH,QAAQ,KAAK,KAAK;IACpB;IAEA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,kBAAkB;YAClB,IAAI,WAAW,MAAM;gBACnB,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,MAAM,GAAG,KAAK,MAAM;gBAC1B,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YAEA,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAClC,QAAQ,IAAI,CAAC,QAAQ,GAAG,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAE5E,4DAA4D;gBAC5D,IAAI,MAAM,KAAK,EAAE;oBACf,IAAI;wBACF,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;wBACtB,MAAM,MAAM,MAAM,sHAAA,CAAA,UAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC;wBAEnD,IAAI,KAAK;4BACP,QAAQ,GAAG,GAAG;gCACZ,IAAI,IAAI,GAAG,CAAC,QAAQ;gCACpB,MAAM,IAAI,IAAI;gCACd,WAAW,IAAI,SAAS;gCACxB,QAAQ,IAAI,MAAM;4BACpB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;YACF;YAEA,OAAO;QACT;QAEA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAElD,0CAA0C;YAC1C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAE5C,OAAO;QACT;IACF;IAEA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;QACf,SAAS;IACX;IAEA,QAAQ;QACN,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE;YAChD,QAAQ,GAAG,CAAC,mBAAmB;gBAC7B,QAAQ,KAAK,EAAE;gBACf,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;YACnB;QACF;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,QAAQ,GAAG,CAAC,oBAAoB;gBAC9B,QAAQ,OAAO,MAAM,SAAS,MAAM;YACtC;QACF;IACF;IAEA,OAAO,oDAAyB;AAClC;uCAEe", "debugId": null}}, {"offset": {"line": 1636, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/auth/utils.ts"], "sourcesContent": ["import { getServerSession } from 'next-auth/next';\nimport { authOptions } from './config';\nimport { UserRole, UserStatus } from '@/models/User';\nimport { NextRequest } from 'next/server';\n\n// Get current session server-side\nexport async function getCurrentSession() {\n  return await getServerSession(authOptions);\n}\n\n// Get current user from session\nexport async function getCurrentUser() {\n  const session = await getCurrentSession();\n  return session?.user || null;\n}\n\n// Check if user is authenticated\nexport async function isAuthenticated(): Promise<boolean> {\n  const session = await getCurrentSession();\n  return !!session?.user && session.user.status === UserStatus.ACTIVE;\n}\n\n// Check if user has specific role\nexport async function hasRole(role: UserRole): Promise<boolean> {\n  const user = await getCurrentUser();\n  return user?.role === role;\n}\n\n// Check if user has any of the specified roles\nexport async function hasAnyRole(roles: UserRole[]): Promise<boolean> {\n  const user = await getCurrentUser();\n  return user ? roles.includes(user.role) : false;\n}\n\n// Check if user is super admin\nexport async function isSuperAdmin(): Promise<boolean> {\n  return await hasRole(UserRole.SUPER_ADMIN);\n}\n\n// Check if user is gym owner\nexport async function isGymOwner(): Promise<boolean> {\n  return await hasRole(UserRole.GYM_OWNER);\n}\n\n// Check if user is gym admin\nexport async function isGymAdmin(): Promise<boolean> {\n  return await hasRole(UserRole.GYM_ADMIN);\n}\n\n// Check if user is gym staff\nexport async function isGymStaff(): Promise<boolean> {\n  return await hasRole(UserRole.GYM_STAFF);\n}\n\n// Check if user is member\nexport async function isMember(): Promise<boolean> {\n  return await hasRole(UserRole.MEMBER);\n}\n\n// Check if user has admin privileges (super admin, gym owner, or gym admin)\nexport async function hasAdminPrivileges(): Promise<boolean> {\n  return await hasAnyRole([UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN]);\n}\n\n// Check if user has staff privileges (admin privileges or gym staff)\nexport async function hasStaffPrivileges(): Promise<boolean> {\n  return await hasAnyRole([\n    UserRole.SUPER_ADMIN, \n    UserRole.GYM_OWNER, \n    UserRole.GYM_ADMIN, \n    UserRole.GYM_STAFF\n  ]);\n}\n\n// Check if user has access to specific gym\nexport async function hasGymAccess(gymId: string): Promise<boolean> {\n  const user = await getCurrentUser();\n  \n  if (!user) return false;\n  \n  // Super admin has access to all gyms\n  if (user.role === UserRole.SUPER_ADMIN) return true;\n  \n  // Check if user's primary gym matches\n  if (user.gymId === gymId) return true;\n  \n  // Check if user has access to multiple gyms\n  if (user.gymIds && user.gymIds.includes(gymId)) return true;\n  \n  return false;\n}\n\n// Get user's accessible gym IDs\nexport async function getAccessibleGymIds(): Promise<string[]> {\n  const user = await getCurrentUser();\n  \n  if (!user) return [];\n  \n  // Super admin has access to all gyms (return empty array to indicate all)\n  if (user.role === UserRole.SUPER_ADMIN) return [];\n  \n  // Collect all accessible gym IDs\n  const gymIds: string[] = [];\n  \n  if (user.gymId) gymIds.push(user.gymId);\n  if (user.gymIds) gymIds.push(...user.gymIds);\n  \n  // Remove duplicates\n  return [...new Set(gymIds)];\n}\n\n// Role hierarchy for permission checking\nconst ROLE_HIERARCHY = {\n  [UserRole.SUPER_ADMIN]: 5,\n  [UserRole.GYM_OWNER]: 4,\n  [UserRole.GYM_ADMIN]: 3,\n  [UserRole.GYM_STAFF]: 2,\n  [UserRole.MEMBER]: 1\n};\n\n// Check if user has higher or equal role level\nexport async function hasMinimumRole(minimumRole: UserRole): Promise<boolean> {\n  const user = await getCurrentUser();\n  \n  if (!user) return false;\n  \n  const userLevel = ROLE_HIERARCHY[user.role] || 0;\n  const minimumLevel = ROLE_HIERARCHY[minimumRole] || 0;\n  \n  return userLevel >= minimumLevel;\n}\n\n// Authorization error class\nexport class AuthorizationError extends Error {\n  constructor(message: string = 'Unauthorized access') {\n    super(message);\n    this.name = 'AuthorizationError';\n  }\n}\n\n// Require authentication (throws error if not authenticated)\nexport async function requireAuth() {\n  const authenticated = await isAuthenticated();\n  if (!authenticated) {\n    throw new AuthorizationError('Authentication required');\n  }\n}\n\n// Require specific role (throws error if user doesn't have role)\nexport async function requireRole(role: UserRole) {\n  await requireAuth();\n  const hasRequiredRole = await hasRole(role);\n  if (!hasRequiredRole) {\n    throw new AuthorizationError(`${role} role required`);\n  }\n}\n\n// Require any of the specified roles\nexport async function requireAnyRole(roles: UserRole[]) {\n  await requireAuth();\n  const hasRequiredRole = await hasAnyRole(roles);\n  if (!hasRequiredRole) {\n    throw new AuthorizationError(`One of the following roles required: ${roles.join(', ')}`);\n  }\n}\n\n// Require minimum role level\nexport async function requireMinimumRole(minimumRole: UserRole) {\n  await requireAuth();\n  const hasMinRole = await hasMinimumRole(minimumRole);\n  if (!hasMinRole) {\n    throw new AuthorizationError(`Minimum role ${minimumRole} required`);\n  }\n}\n\n// Require gym access\nexport async function requireGymAccess(gymId: string) {\n  await requireAuth();\n  const hasAccess = await hasGymAccess(gymId);\n  if (!hasAccess) {\n    throw new AuthorizationError('Access to this gym is not authorized');\n  }\n}\n\n// Require admin privileges\nexport async function requireAdminPrivileges() {\n  await requireAnyRole([UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN]);\n}\n\n// Require staff privileges\nexport async function requireStaffPrivileges() {\n  await requireAnyRole([\n    UserRole.SUPER_ADMIN, \n    UserRole.GYM_OWNER, \n    UserRole.GYM_ADMIN, \n    UserRole.GYM_STAFF\n  ]);\n}\n\n// Extract bearer token from request headers\nexport function extractBearerToken(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization');\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return null;\n  }\n  return authHeader.substring(7);\n}\n\n// Validate session status\nexport function validateSessionStatus(status: UserStatus): boolean {\n  return status === UserStatus.ACTIVE;\n}\n\n// Get role display name\nexport function getRoleDisplayName(role: UserRole): string {\n  const roleNames = {\n    [UserRole.SUPER_ADMIN]: 'Super Administrator',\n    [UserRole.GYM_OWNER]: 'Gym Owner',\n    [UserRole.GYM_ADMIN]: 'Gym Administrator',\n    [UserRole.GYM_STAFF]: 'Gym Staff',\n    [UserRole.MEMBER]: 'Member'\n  };\n  \n  return roleNames[role] || role;\n}\n\n// Get status display name\nexport function getStatusDisplayName(status: UserStatus): string {\n  const statusNames = {\n    [UserStatus.ACTIVE]: 'Active',\n    [UserStatus.INACTIVE]: 'Inactive',\n    [UserStatus.SUSPENDED]: 'Suspended',\n    [UserStatus.PENDING]: 'Pending'\n  };\n  \n  return statusNames[status] || status;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAIO,eAAe;IACpB,OAAO,MAAM,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,8HAAA,CAAA,cAAW;AAC3C;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,SAAS,QAAQ;AAC1B;AAGO,eAAe;IACpB,MAAM,UAAU,MAAM;IACtB,OAAO,CAAC,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,MAAM;AACrE;AAGO,eAAe,QAAQ,IAAc;IAC1C,MAAM,OAAO,MAAM;IACnB,OAAO,MAAM,SAAS;AACxB;AAGO,eAAe,WAAW,KAAiB;IAChD,MAAM,OAAO,MAAM;IACnB,OAAO,OAAO,MAAM,QAAQ,CAAC,KAAK,IAAI,IAAI;AAC5C;AAGO,eAAe;IACpB,OAAO,MAAM,QAAQ,uHAAA,CAAA,WAAQ,CAAC,WAAW;AAC3C;AAGO,eAAe;IACpB,OAAO,MAAM,QAAQ,uHAAA,CAAA,WAAQ,CAAC,SAAS;AACzC;AAGO,eAAe;IACpB,OAAO,MAAM,QAAQ,uHAAA,CAAA,WAAQ,CAAC,SAAS;AACzC;AAGO,eAAe;IACpB,OAAO,MAAM,QAAQ,uHAAA,CAAA,WAAQ,CAAC,SAAS;AACzC;AAGO,eAAe;IACpB,OAAO,MAAM,QAAQ,uHAAA,CAAA,WAAQ,CAAC,MAAM;AACtC;AAGO,eAAe;IACpB,OAAO,MAAM,WAAW;QAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;QAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;QAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;KAAC;AACxF;AAGO,eAAe;IACpB,OAAO,MAAM,WAAW;QACtB,uHAAA,CAAA,WAAQ,CAAC,WAAW;QACpB,uHAAA,CAAA,WAAQ,CAAC,SAAS;QAClB,uHAAA,CAAA,WAAQ,CAAC,SAAS;QAClB,uHAAA,CAAA,WAAQ,CAAC,SAAS;KACnB;AACH;AAGO,eAAe,aAAa,KAAa;IAC9C,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM,OAAO;IAElB,qCAAqC;IACrC,IAAI,KAAK,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,OAAO;IAE/C,sCAAsC;IACtC,IAAI,KAAK,KAAK,KAAK,OAAO,OAAO;IAEjC,4CAA4C;IAC5C,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,QAAQ,OAAO;IAEvD,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM,OAAO,EAAE;IAEpB,0EAA0E;IAC1E,IAAI,KAAK,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,EAAE,OAAO,EAAE;IAEjD,iCAAiC;IACjC,MAAM,SAAmB,EAAE;IAE3B,IAAI,KAAK,KAAK,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK;IACtC,IAAI,KAAK,MAAM,EAAE,OAAO,IAAI,IAAI,KAAK,MAAM;IAE3C,oBAAoB;IACpB,OAAO;WAAI,IAAI,IAAI;KAAQ;AAC7B;AAEA,yCAAyC;AACzC,MAAM,iBAAiB;IACrB,CAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,EAAE;IACxB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;IACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;IACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;IACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,EAAE;AACrB;AAGO,eAAe,eAAe,WAAqB;IACxD,MAAM,OAAO,MAAM;IAEnB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,YAAY,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI;IAC/C,MAAM,eAAe,cAAc,CAAC,YAAY,IAAI;IAEpD,OAAO,aAAa;AACtB;AAGO,MAAM,2BAA2B;IACtC,YAAY,UAAkB,qBAAqB,CAAE;QACnD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAGO,eAAe;IACpB,MAAM,gBAAgB,MAAM;IAC5B,IAAI,CAAC,eAAe;QAClB,MAAM,IAAI,mBAAmB;IAC/B;AACF;AAGO,eAAe,YAAY,IAAc;IAC9C,MAAM;IACN,MAAM,kBAAkB,MAAM,QAAQ;IACtC,IAAI,CAAC,iBAAiB;QACpB,MAAM,IAAI,mBAAmB,GAAG,KAAK,cAAc,CAAC;IACtD;AACF;AAGO,eAAe,eAAe,KAAiB;IACpD,MAAM;IACN,MAAM,kBAAkB,MAAM,WAAW;IACzC,IAAI,CAAC,iBAAiB;QACpB,MAAM,IAAI,mBAAmB,CAAC,qCAAqC,EAAE,MAAM,IAAI,CAAC,OAAO;IACzF;AACF;AAGO,eAAe,mBAAmB,WAAqB;IAC5D,MAAM;IACN,MAAM,aAAa,MAAM,eAAe;IACxC,IAAI,CAAC,YAAY;QACf,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,YAAY,SAAS,CAAC;IACrE;AACF;AAGO,eAAe,iBAAiB,KAAa;IAClD,MAAM;IACN,MAAM,YAAY,MAAM,aAAa;IACrC,IAAI,CAAC,WAAW;QACd,MAAM,IAAI,mBAAmB;IAC/B;AACF;AAGO,eAAe;IACpB,MAAM,eAAe;QAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW;QAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;QAAE,uHAAA,CAAA,WAAQ,CAAC,SAAS;KAAC;AACrF;AAGO,eAAe;IACpB,MAAM,eAAe;QACnB,uHAAA,CAAA,WAAQ,CAAC,WAAW;QACpB,uHAAA,CAAA,WAAQ,CAAC,SAAS;QAClB,uHAAA,CAAA,WAAQ,CAAC,SAAS;QAClB,uHAAA,CAAA,WAAQ,CAAC,SAAS;KACnB;AACH;AAGO,SAAS,mBAAmB,OAAoB;IACrD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO;IACT;IACA,OAAO,WAAW,SAAS,CAAC;AAC9B;AAGO,SAAS,sBAAsB,MAAkB;IACtD,OAAO,WAAW,uHAAA,CAAA,aAAU,CAAC,MAAM;AACrC;AAGO,SAAS,mBAAmB,IAAc;IAC/C,MAAM,YAAY;QAChB,CAAC,uHAAA,CAAA,WAAQ,CAAC,WAAW,CAAC,EAAE;QACxB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;QACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;QACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,EAAE;QACtB,CAAC,uHAAA,CAAA,WAAQ,CAAC,MAAM,CAAC,EAAE;IACrB;IAEA,OAAO,SAAS,CAAC,KAAK,IAAI;AAC5B;AAGO,SAAS,qBAAqB,MAAkB;IACrD,MAAM,cAAc;QAClB,CAAC,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,EAAE;QACrB,CAAC,uHAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,EAAE;QACvB,CAAC,uHAAA,CAAA,aAAU,CAAC,SAAS,CAAC,EAAE;QACxB,CAAC,uHAAA,CAAA,aAAU,CAAC,OAAO,CAAC,EAAE;IACxB;IAEA,OAAO,WAAW,CAAC,OAAO,IAAI;AAChC", "debugId": null}}, {"offset": {"line": 1850, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport { connectToDatabase } from '@/lib/database/connection';\nimport User, { UserRole, UserStatus } from '@/models/User';\nimport { userRegistrationSchema, paginationSchema } from '@/lib/validations/schemas';\nimport { \n  createSuccessResponse, \n  createErrorResponse,\n  withErrorHandling\n} from '@/lib/utils/api';\nimport { requireAuth } from '@/lib/auth/middleware';\nimport { requireAdminPrivileges } from '@/lib/auth/utils';\n\n/**\n * GET /api/users - List users with pagination and filtering\n * Requires admin privileges\n */\nexport const GET = requireAuth(withErrorHandling(async (request: NextRequest) => {\n  await connectToDatabase();\n  \n  // Check admin privileges\n  await requireAdminPrivileges();\n  \n  const { searchParams } = new URL(request.url);\n  const { page, limit, search, sortBy, sortOrder } = paginationSchema.parse({\n    page: searchParams.get('page') || '1',\n    limit: searchParams.get('limit') || '10',\n    search: searchParams.get('search'),\n    sortBy: searchParams.get('sortBy') || 'createdAt',\n    sortOrder: searchParams.get('sortOrder') || 'desc'\n  });\n  \n  // Get filter parameters\n  const role = searchParams.get('role') as UserRole;\n  const status = searchParams.get('status') as UserStatus;\n  const gymId = searchParams.get('gymId');\n  \n  // Build query\n  const query: any = {};\n  \n  // Search functionality\n  if (search) {\n    query.$or = [\n      { firstName: { $regex: search, $options: 'i' } },\n      { lastName: { $regex: search, $options: 'i' } },\n      { email: { $regex: search, $options: 'i' } }\n    ];\n  }\n  \n  // Filter by role\n  if (role && Object.values(UserRole).includes(role)) {\n    query.role = role;\n  }\n  \n  // Filter by status\n  if (status && Object.values(UserStatus).includes(status)) {\n    query.status = status;\n  }\n  \n  // Filter by gym\n  if (gymId) {\n    query.$or = [\n      { gymId: gymId },\n      { gymIds: { $in: [gymId] } }\n    ];\n  }\n  \n  // Calculate pagination\n  const skip = (page - 1) * limit;\n  \n  // Build sort object\n  const sort: any = {};\n  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;\n  \n  // Execute queries\n  const [users, total] = await Promise.all([\n    User.find(query)\n      .select('-password -passwordResetToken -emailVerificationToken')\n      .sort(sort)\n      .skip(skip)\n      .limit(limit)\n      .lean(),\n    User.countDocuments(query)\n  ]);\n  \n  const totalPages = Math.ceil(total / limit);\n  \n  return createSuccessResponse(users, 'Users retrieved successfully', {\n    pagination: {\n      page,\n      limit,\n      total,\n      totalPages\n    }\n  });\n}));\n\n/**\n * POST /api/users - Create new user\n * Requires admin privileges\n */\nexport const POST = requireAuth(withErrorHandling(async (request: NextRequest) => {\n  await connectToDatabase();\n  \n  // Check admin privileges\n  await requireAdminPrivileges();\n  \n  const body = await request.json();\n  \n  // Validate request body\n  const validatedData = userRegistrationSchema.parse(body);\n  \n  // Check if user already exists\n  const existingUser = await User.findOne({ \n    email: validatedData.email.toLowerCase() \n  });\n  \n  if (existingUser) {\n    return createErrorResponse('User with this email already exists', 409);\n  }\n  \n  // Validate gym association for non-super-admin users\n  if (validatedData.role !== UserRole.SUPER_ADMIN && !validatedData.gymId) {\n    return createErrorResponse('gymId is required for non-super-admin users', 400);\n  }\n  \n  // Create user\n  const userData = {\n    email: validatedData.email.toLowerCase(),\n    password: validatedData.password,\n    firstName: validatedData.firstName,\n    lastName: validatedData.lastName,\n    phone: validatedData.phone,\n    role: validatedData.role,\n    gymId: validatedData.gymId,\n    dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : undefined,\n    gender: validatedData.gender,\n    address: validatedData.address,\n    status: UserStatus.ACTIVE // Admin-created users are active by default\n  };\n  \n  const user = new User(userData);\n  await user.save();\n  \n  // Remove sensitive data from response\n  const userResponse = user.toObject();\n  delete userResponse.password;\n  delete userResponse.passwordResetToken;\n  delete userResponse.emailVerificationToken;\n  \n  return createSuccessResponse(userResponse, 'User created successfully', null, 201);\n}));\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAKA;AACA;;;;;;;AAMO,MAAM,MAAM,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IACtD,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAEtB,yBAAyB;IACzB,MAAM,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD;IAE3B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,sIAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC;QACxE,MAAM,aAAa,GAAG,CAAC,WAAW;QAClC,OAAO,aAAa,GAAG,CAAC,YAAY;QACpC,QAAQ,aAAa,GAAG,CAAC;QACzB,QAAQ,aAAa,GAAG,CAAC,aAAa;QACtC,WAAW,aAAa,GAAG,CAAC,gBAAgB;IAC9C;IAEA,wBAAwB;IACxB,MAAM,OAAO,aAAa,GAAG,CAAC;IAC9B,MAAM,SAAS,aAAa,GAAG,CAAC;IAChC,MAAM,QAAQ,aAAa,GAAG,CAAC;IAE/B,cAAc;IACd,MAAM,QAAa,CAAC;IAEpB,uBAAuB;IACvB,IAAI,QAAQ;QACV,MAAM,GAAG,GAAG;YACV;gBAAE,WAAW;oBAAE,QAAQ;oBAAQ,UAAU;gBAAI;YAAE;YAC/C;gBAAE,UAAU;oBAAE,QAAQ;oBAAQ,UAAU;gBAAI;YAAE;YAC9C;gBAAE,OAAO;oBAAE,QAAQ;oBAAQ,UAAU;gBAAI;YAAE;SAC5C;IACH;IAEA,iBAAiB;IACjB,IAAI,QAAQ,OAAO,MAAM,CAAC,uHAAA,CAAA,WAAQ,EAAE,QAAQ,CAAC,OAAO;QAClD,MAAM,IAAI,GAAG;IACf;IAEA,mBAAmB;IACnB,IAAI,UAAU,OAAO,MAAM,CAAC,uHAAA,CAAA,aAAU,EAAE,QAAQ,CAAC,SAAS;QACxD,MAAM,MAAM,GAAG;IACjB;IAEA,gBAAgB;IAChB,IAAI,OAAO;QACT,MAAM,GAAG,GAAG;YACV;gBAAE,OAAO;YAAM;YACf;gBAAE,QAAQ;oBAAE,KAAK;wBAAC;qBAAM;gBAAC;YAAE;SAC5B;IACH;IAEA,uBAAuB;IACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAE1B,oBAAoB;IACpB,MAAM,OAAY,CAAC;IACnB,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;IAE1C,kBAAkB;IAClB,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;QACvC,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC,OACP,MAAM,CAAC,yDACP,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;QACP,uHAAA,CAAA,UAAI,CAAC,cAAc,CAAC;KACrB;IAED,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;IAErC,OAAO,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,gCAAgC;QAClE,YAAY;YACV;YACA;YACA;YACA;QACF;IACF;AACF;AAMO,MAAM,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IACvD,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;IAEtB,yBAAyB;IACzB,MAAM,CAAA,GAAA,6HAAA,CAAA,yBAAsB,AAAD;IAE3B,MAAM,OAAO,MAAM,QAAQ,IAAI;IAE/B,wBAAwB;IACxB,MAAM,gBAAgB,sIAAA,CAAA,yBAAsB,CAAC,KAAK,CAAC;IAEnD,+BAA+B;IAC/B,MAAM,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QACtC,OAAO,cAAc,KAAK,CAAC,WAAW;IACxC;IAEA,IAAI,cAAc;QAChB,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,uCAAuC;IACpE;IAEA,qDAAqD;IACrD,IAAI,cAAc,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,IAAI,CAAC,cAAc,KAAK,EAAE;QACvE,OAAO,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,+CAA+C;IAC5E;IAEA,cAAc;IACd,MAAM,WAAW;QACf,OAAO,cAAc,KAAK,CAAC,WAAW;QACtC,UAAU,cAAc,QAAQ;QAChC,WAAW,cAAc,SAAS;QAClC,UAAU,cAAc,QAAQ;QAChC,OAAO,cAAc,KAAK;QAC1B,MAAM,cAAc,IAAI;QACxB,OAAO,cAAc,KAAK;QAC1B,aAAa,cAAc,WAAW,GAAG,IAAI,KAAK,cAAc,WAAW,IAAI;QAC/E,QAAQ,cAAc,MAAM;QAC5B,SAAS,cAAc,OAAO;QAC9B,QAAQ,uHAAA,CAAA,aAAU,CAAC,MAAM,CAAC,4CAA4C;IACxE;IAEA,MAAM,OAAO,IAAI,uHAAA,CAAA,UAAI,CAAC;IACtB,MAAM,KAAK,IAAI;IAEf,sCAAsC;IACtC,MAAM,eAAe,KAAK,QAAQ;IAClC,OAAO,aAAa,QAAQ;IAC5B,OAAO,aAAa,kBAAkB;IACtC,OAAO,aAAa,sBAAsB;IAE1C,OAAO,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,6BAA6B,MAAM;AAChF", "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\ninterface ConnectionCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongooseCache: ConnectionCache | undefined;\n}\n\nlet cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};\n\nif (!global.mongooseCache) {\n  global.mongooseCache = cached;\n}\n\nexport async function connectToDatabase(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4,\n    };\n\n    const mongoUri = process.env.MONGODB_URI;\n    if (!mongoUri) {\n      throw new Error('MONGODB_URI environment variable is not defined');\n    }\n\n    cached.promise = mongoose.connect(mongoUri, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n    console.log('✅ Connected to MongoDB');\n    return cached.conn;\n  } catch (error) {\n    cached.promise = null;\n    console.error('❌ MongoDB connection error:', error);\n    throw error;\n  }\n}\n\nexport async function disconnectFromDatabase(): Promise<void> {\n  if (cached.conn) {\n    await cached.conn.disconnect();\n    cached.conn = null;\n    cached.promise = null;\n    console.log('🔌 Disconnected from MongoDB');\n  }\n}\n\n// Connection event handlers\nmongoose.connection.on('connected', () => {\n  console.log('🔗 Mongoose connected to MongoDB');\n});\n\nmongoose.connection.on('error', (err) => {\n  console.error('❌ Mongoose connection error:', err);\n});\n\nmongoose.connection.on('disconnected', () => {\n  console.log('🔌 Mongoose disconnected from MongoDB');\n});\n\n// Graceful shutdown\nprocess.on('SIGINT', async () => {\n  await disconnectFromDatabase();\n  process.exit(0);\n});\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,IAAI,SAA0B,OAAO,aAAa,IAAI;IACpD,MAAM;IACN,SAAS;AACX;AAEA,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG;AACzB;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;IAC9C;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;AACF;AAEA,4BAA4B;AAC5B,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa;IAClC,QAAQ,GAAG,CAAC;AACd;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;IAC/B,QAAQ,KAAK,CAAC,gCAAgC;AAChD;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB;IACrC,QAAQ,GAAG,CAAC;AACd;AAEA,oBAAoB;AACpB,QAAQ,EAAE,CAAC,UAAU;IACnB,MAAM;IACN,QAAQ,IAAI,CAAC;AACf", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\n// User roles enum\nexport enum UserRole {\n  SUPER_ADMIN = 'super_admin',\n  GYM_OWNER = 'gym_owner',\n  GYM_ADMIN = 'gym_admin',\n  GYM_STAFF = 'gym_staff',\n  MEMBER = 'member'\n}\n\n// User status enum\nexport enum UserStatus {\n  ACTIVE = 'active',\n  INACTIVE = 'inactive',\n  SUSPENDED = 'suspended',\n  PENDING = 'pending'\n}\n\n// User interface\nexport interface IUser extends Document {\n  // Basic Information\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  avatar?: string;\n  \n  // Role and Permissions\n  role: UserRole;\n  status: UserStatus;\n  \n  // Multi-tenant Association\n  gymId?: string; // Optional for super_admin, required for others\n  gymIds?: string[]; // For users who can access multiple gyms (super_admin, gym_owner)\n  \n  // Authentication\n  emailVerified?: Date;\n  emailVerificationToken?: string;\n  passwordResetToken?: string;\n  passwordResetExpires?: Date;\n  lastLogin?: Date;\n  loginAttempts?: number;\n  lockUntil?: Date;\n  \n  // Profile Information\n  dateOfBirth?: Date;\n  gender?: 'male' | 'female' | 'other';\n  address?: {\n    street?: string;\n    city?: string;\n    state?: string;\n    zipCode?: string;\n    country?: string;\n  };\n  \n  // Preferences\n  preferences?: {\n    notifications?: {\n      email?: boolean;\n      sms?: boolean;\n      whatsapp?: boolean;\n    };\n    language?: string;\n    timezone?: string;\n  };\n  \n  // Timestamps\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Virtual properties\n  fullName: string;\n  isLocked: boolean;\n  \n  // Methods\n  comparePassword(candidatePassword: string): Promise<boolean>;\n  generatePasswordResetToken(): string;\n  generateEmailVerificationToken(): string;\n  incrementLoginAttempts(): Promise<void>;\n  resetLoginAttempts(): Promise<void>;\n}\n\n// User schema\nconst UserSchema = new Schema<IUser>({\n  // Basic Information\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [8, 'Password must be at least 8 characters long'],\n    select: false // Don't include password in queries by default\n  },\n  firstName: {\n    type: String,\n    required: [true, 'First name is required'],\n    trim: true,\n    maxlength: [50, 'First name cannot exceed 50 characters']\n  },\n  lastName: {\n    type: String,\n    required: [true, 'Last name is required'],\n    trim: true,\n    maxlength: [50, 'Last name cannot exceed 50 characters']\n  },\n  phone: {\n    type: String,\n    trim: true,\n    match: [/^\\+?[\\d\\s\\-\\(\\)]+$/, 'Please enter a valid phone number']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  \n  // Role and Permissions\n  role: {\n    type: String,\n    enum: Object.values(UserRole),\n    required: [true, 'User role is required'],\n    default: UserRole.MEMBER\n  },\n  status: {\n    type: String,\n    enum: Object.values(UserStatus),\n    default: UserStatus.PENDING\n  },\n  \n  // Multi-tenant Association\n  gymId: {\n    type: String,\n    ref: 'Gym',\n    required: function(this: IUser) {\n      // gymId is required for all roles except super_admin\n      return this.role !== UserRole.SUPER_ADMIN;\n    },\n    index: true\n  },\n  gymIds: [{\n    type: String,\n    ref: 'Gym'\n  }],\n  \n  // Authentication\n  emailVerified: {\n    type: Date\n  },\n  emailVerificationToken: {\n    type: String,\n    select: false\n  },\n  passwordResetToken: {\n    type: String,\n    select: false\n  },\n  passwordResetExpires: {\n    type: Date,\n    select: false\n  },\n  lastLogin: {\n    type: Date\n  },\n  loginAttempts: {\n    type: Number,\n    default: 0\n  },\n  lockUntil: {\n    type: Date\n  },\n  \n  // Profile Information\n  dateOfBirth: {\n    type: Date\n  },\n  gender: {\n    type: String,\n    enum: ['male', 'female', 'other']\n  },\n  address: {\n    street: { type: String, trim: true },\n    city: { type: String, trim: true },\n    state: { type: String, trim: true },\n    zipCode: { type: String, trim: true },\n    country: { type: String, trim: true, default: 'US' }\n  },\n  \n  // Preferences\n  preferences: {\n    notifications: {\n      email: { type: Boolean, default: true },\n      sms: { type: Boolean, default: false },\n      whatsapp: { type: Boolean, default: false }\n    },\n    language: { type: String, default: 'en' },\n    timezone: { type: String, default: 'UTC' }\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for performance\nUserSchema.index({ email: 1 });\nUserSchema.index({ gymId: 1, role: 1 });\nUserSchema.index({ status: 1 });\nUserSchema.index({ createdAt: -1 });\n\n// Virtual properties\nUserSchema.virtual('fullName').get(function(this: IUser) {\n  return `${this.firstName} ${this.lastName}`.trim();\n});\n\nUserSchema.virtual('isLocked').get(function(this: IUser) {\n  return !!(this.lockUntil && this.lockUntil > new Date());\n});\n\n// Pre-save middleware\nUserSchema.pre('save', async function(this: IUser, next) {\n  // Only hash password if it's modified\n  if (!this.isModified('password')) return next();\n  \n  try {\n    // Hash password with cost of 12\n    const salt = await bcrypt.genSalt(12);\n    this.password = await bcrypt.hash(this.password, salt);\n    next();\n  } catch (error) {\n    next(error as Error);\n  }\n});\n\n// Pre-save middleware for gym association\nUserSchema.pre('save', function(this: IUser, next) {\n  // For super_admin, ensure gymIds array exists\n  if (this.role === UserRole.SUPER_ADMIN && !this.gymIds) {\n    this.gymIds = [];\n  }\n  \n  // For gym_owner, add gymId to gymIds array if not already present\n  if (this.role === UserRole.GYM_OWNER && this.gymId) {\n    if (!this.gymIds) this.gymIds = [];\n    if (!this.gymIds.includes(this.gymId)) {\n      this.gymIds.push(this.gymId);\n    }\n  }\n  \n  next();\n});\n\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {\n  if (!this.password) return false;\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\nUserSchema.methods.generatePasswordResetToken = function(): string {\n  const resetToken = Math.random().toString(36).substring(2, 15) + \n                    Math.random().toString(36).substring(2, 15);\n  \n  this.passwordResetToken = resetToken;\n  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\n  \n  return resetToken;\n};\n\nUserSchema.methods.generateEmailVerificationToken = function(): string {\n  const verificationToken = Math.random().toString(36).substring(2, 15) + \n                           Math.random().toString(36).substring(2, 15);\n  \n  this.emailVerificationToken = verificationToken;\n  \n  return verificationToken;\n};\n\nUserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {\n  // If we have a previous lock that has expired, restart at 1\n  if (this.lockUntil && this.lockUntil < new Date()) {\n    return this.updateOne({\n      $unset: { lockUntil: 1 },\n      $set: { loginAttempts: 1 }\n    });\n  }\n  \n  const updates: any = { $inc: { loginAttempts: 1 } };\n  \n  // Lock account after 5 failed attempts for 2 hours\n  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {\n    updates.$set = { lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000) };\n  }\n  \n  return this.updateOne(updates);\n};\n\nUserSchema.methods.resetLoginAttempts = async function(): Promise<void> {\n  return this.updateOne({\n    $unset: { loginAttempts: 1, lockUntil: 1 },\n    $set: { lastLogin: new Date() }\n  });\n};\n\n// Export the model\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,IAAA,AAAK,kCAAA;;;;;;WAAA;;AASL,IAAA,AAAK,oCAAA;;;;;WAAA;;AAwEZ,cAAc;AACd,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAAQ;IACnC,oBAAoB;IACpB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAA8C;QAC7D,QAAQ,MAAM,+CAA+C;IAC/D;IACA,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAI;SAAwC;IAC1D;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,OAAO;YAAC;YAAsB;SAAoC;IACpE;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IAEA,uBAAuB;IACvB,MAAM;QACJ,MAAM;QACN,MAAM,OAAO,MAAM,CAAC;QACpB,UAAU;YAAC;YAAM;SAAwB;QACzC,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,MAAM,OAAO,MAAM,CAAC;QACpB,OAAO;IACT;IAEA,2BAA2B;IAC3B,OAAO;QACL,MAAM;QACN,KAAK;QACL,UAAU;YACR,qDAAqD;YACrD,OAAO,IAAI,CAAC,IAAI;QAClB;QACA,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,KAAK;QACP;KAAE;IAEF,iBAAiB;IACjB,eAAe;QACb,MAAM;IACR;IACA,wBAAwB;QACtB,MAAM;QACN,QAAQ;IACV;IACA,oBAAoB;QAClB,MAAM;QACN,QAAQ;IACV;IACA,sBAAsB;QACpB,MAAM;QACN,QAAQ;IACV;IACA,WAAW;QACT,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;IACR;IAEA,sBAAsB;IACtB,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAQ;YAAU;SAAQ;IACnC;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,MAAM;QAAK;QACnC,MAAM;YAAE,MAAM;YAAQ,MAAM;QAAK;QACjC,OAAO;YAAE,MAAM;YAAQ,MAAM;QAAK;QAClC,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;QACpC,SAAS;YAAE,MAAM;YAAQ,MAAM;YAAM,SAAS;QAAK;IACrD;IAEA,cAAc;IACd,aAAa;QACX,eAAe;YACb,OAAO;gBAAE,MAAM;gBAAS,SAAS;YAAK;YACtC,KAAK;gBAAE,MAAM;gBAAS,SAAS;YAAM;YACrC,UAAU;gBAAE,MAAM;gBAAS,SAAS;YAAM;QAC5C;QACA,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;IAC3C;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,0BAA0B;AAC1B,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,OAAO;IAAG,MAAM;AAAE;AACrC,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AAEjC,qBAAqB;AACrB,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI;AAClD;AAEA,WAAW,OAAO,CAAC,YAAY,GAAG,CAAC;IACjC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,MAAM;AACzD;AAEA,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,eAA4B,IAAI;IACrD,sCAAsC;IACtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,gCAAgC;QAChC,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,0CAA0C;AAC1C,WAAW,GAAG,CAAC,QAAQ,SAAsB,IAAI;IAC/C,8CAA8C;IAC9C,IAAI,IAAI,CAAC,IAAI,sBAA6B,CAAC,IAAI,CAAC,MAAM,EAAE;QACtD,IAAI,CAAC,MAAM,GAAG,EAAE;IAClB;IAEA,kEAAkE;IAClE,IAAI,IAAI,CAAC,IAAI,oBAA2B,IAAI,CAAC,KAAK,EAAE;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG;YACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;QAC7B;IACF;IAEA;AACF;AAEA,mBAAmB;AACnB,WAAW,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAyB;IAC3E,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;IAC3B,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,WAAW,OAAO,CAAC,0BAA0B,GAAG;IAC9C,MAAM,aAAa,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACzC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAE1D,IAAI,CAAC,kBAAkB,GAAG;IAC1B,IAAI,CAAC,oBAAoB,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,OAAO,aAAa;IAEhF,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,8BAA8B,GAAG;IAClD,MAAM,oBAAoB,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACzC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAEjE,IAAI,CAAC,sBAAsB,GAAG;IAE9B,OAAO;AACT;AAEA,WAAW,OAAO,CAAC,sBAAsB,GAAG;IAC1C,4DAA4D;IAC5D,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,QAAQ;QACjD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ;gBAAE,WAAW;YAAE;YACvB,MAAM;gBAAE,eAAe;YAAE;QAC3B;IACF;IAEA,MAAM,UAAe;QAAE,MAAM;YAAE,eAAe;QAAE;IAAE;IAElD,mDAAmD;IACnD,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD,QAAQ,IAAI,GAAG;YAAE,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK;QAAM;IACxE;IAEA,OAAO,IAAI,CAAC,SAAS,CAAC;AACxB;AAEA,WAAW,OAAO,CAAC,kBAAkB,GAAG;IACtC,OAAO,IAAI,CAAC,SAAS,CAAC;QACpB,QAAQ;YAAE,eAAe;YAAG,WAAW;QAAE;QACzC,MAAM;YAAE,WAAW,IAAI;QAAO;IAChC;AACF;uCAGe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/Gym.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IGym extends Document {\n  _id: string;\n  name: string;\n  subdomain: string;\n  domain: string;\n  logo?: string;\n  description?: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    zipCode: string;\n    country: string;\n  };\n  contact: {\n    phone: string;\n    email: string;\n    website?: string;\n  };\n  settings: {\n    timezone: string;\n    currency: string;\n    language: string;\n    dateFormat: string;\n    businessHours: {\n      [key: string]: {\n        open: string;\n        close: string;\n        isOpen: boolean;\n      };\n    };\n  };\n  subscription: {\n    plan: 'free' | 'basic' | 'premium' | 'enterprise';\n    status: 'active' | 'inactive' | 'suspended' | 'cancelled';\n    startDate: Date;\n    endDate?: Date;\n    maxMembers: number;\n    maxTrainers: number;\n  };\n  whatsapp: {\n    enabled: boolean;\n    accountSid?: string;\n    authToken?: string;\n    phoneNumber?: string;\n  };\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst GymSchema = new Schema<IGym>({\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: 100,\n  },\n  subdomain: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: /^[a-z0-9-]+$/,\n    maxlength: 50,\n  },\n  domain: {\n    type: String,\n    trim: true,\n  },\n  logo: {\n    type: String,\n    trim: true,\n  },\n  description: {\n    type: String,\n    trim: true,\n    maxlength: 500,\n  },\n  address: {\n    street: { type: String, required: true, trim: true },\n    city: { type: String, required: true, trim: true },\n    state: { type: String, required: true, trim: true },\n    zipCode: { type: String, required: true, trim: true },\n    country: { type: String, required: true, trim: true, default: 'US' },\n  },\n  contact: {\n    phone: { type: String, required: true, trim: true },\n    email: { type: String, required: true, trim: true, lowercase: true },\n    website: { type: String, trim: true },\n  },\n  settings: {\n    timezone: { type: String, default: 'America/New_York' },\n    currency: { type: String, default: 'USD' },\n    language: { type: String, default: 'en' },\n    dateFormat: { type: String, default: 'MM/DD/YYYY' },\n    businessHours: {\n      type: Map,\n      of: {\n        open: { type: String, default: '06:00' },\n        close: { type: String, default: '22:00' },\n        isOpen: { type: Boolean, default: true },\n      },\n      default: {\n        monday: { open: '06:00', close: '22:00', isOpen: true },\n        tuesday: { open: '06:00', close: '22:00', isOpen: true },\n        wednesday: { open: '06:00', close: '22:00', isOpen: true },\n        thursday: { open: '06:00', close: '22:00', isOpen: true },\n        friday: { open: '06:00', close: '22:00', isOpen: true },\n        saturday: { open: '08:00', close: '20:00', isOpen: true },\n        sunday: { open: '08:00', close: '20:00', isOpen: true },\n      },\n    },\n  },\n  subscription: {\n    plan: {\n      type: String,\n      enum: ['free', 'basic', 'premium', 'enterprise'],\n      default: 'free',\n    },\n    status: {\n      type: String,\n      enum: ['active', 'inactive', 'suspended', 'cancelled'],\n      default: 'active',\n    },\n    startDate: { type: Date, default: Date.now },\n    endDate: Date,\n    maxMembers: { type: Number, default: 50 },\n    maxTrainers: { type: Number, default: 5 },\n  },\n  whatsapp: {\n    enabled: { type: Boolean, default: false },\n    accountSid: String,\n    authToken: String,\n    phoneNumber: String,\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true },\n});\n\n// Indexes\nGymSchema.index({ subdomain: 1 });\nGymSchema.index({ domain: 1 });\nGymSchema.index({ isActive: 1 });\nGymSchema.index({ 'subscription.status': 1 });\n\n// Virtual for full address\nGymSchema.virtual('fullAddress').get(function() {\n  return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;\n});\n\n// Pre-save middleware\nGymSchema.pre('save', function(next) {\n  if (this.isModified('subdomain') || this.isNew) {\n    this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;\n  }\n  next();\n});\n\nexport default mongoose.models.Gym || mongoose.model<IGym>('Gym', GymSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqDA,MAAM,YAAY,IAAI,yGAAA,CAAA,SAAM,CAAO;IACjC,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;IACb;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACnD,MAAM;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACjD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACpD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,SAAS;QAAK;IACrE;IACA,SAAS;QACP,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,WAAW;QAAK;QACnE,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;IACtC;IACA,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAmB;QACtD,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAa;QAClD,eAAe;YACb,MAAM;YACN,IAAI;gBACF,MAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACvC,OAAO;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACxC,QAAQ;oBAAE,MAAM;oBAAS,SAAS;gBAAK;YACzC;YACA,SAAS;gBACP,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACvD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACzD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;YACxD;QACF;IACF;IACA,cAAc;QACZ,MAAM;YACJ,MAAM;YACN,MAAM;gBAAC;gBAAQ;gBAAS;gBAAW;aAAa;YAChD,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,MAAM;gBAAC;gBAAU;gBAAY;gBAAa;aAAY;YACtD,SAAS;QACX;QACA,WAAW;YAAE,MAAM;YAAM,SAAS,KAAK,GAAG;QAAC;QAC3C,SAAS;QACT,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAG;QACxC,aAAa;YAAE,MAAM;YAAQ,SAAS;QAAE;IAC1C;IACA,UAAU;QACR,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,UAAU,KAAK,CAAC;IAAE,WAAW;AAAE;AAC/B,UAAU,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC5B,UAAU,KAAK,CAAC;IAAE,UAAU;AAAE;AAC9B,UAAU,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAE3C,2BAA2B;AAC3B,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;IACnC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC/H;AAEA,sBAAsB;AACtB,UAAU,GAAG,CAAC,QAAQ,SAAS,IAAI;IACjC,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,CAAC,KAAK,EAAE;QAC9C,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,UAAU,IAAI,kBAAkB;IACjF;IACA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAO,OAAO", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/auth/config.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport { MongoDBAdapter } from '@auth/mongodb-adapter';\nimport { connectToDatabase } from '@/lib/database/connection';\nimport User, { UserRole, UserStatus } from '@/models/User';\nimport Gym from '@/models/Gym';\nimport { z } from 'zod';\n\n// Login schema validation\nconst loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n  gymSubdomain: z.string().optional()\n});\n\n// Extended user type for NextAuth\ndeclare module 'next-auth' {\n  interface User {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n    role: UserRole;\n    status: UserStatus;\n    gymId?: string;\n    gymIds?: string[];\n    avatar?: string;\n  }\n\n  interface Session {\n    user: {\n      id: string;\n      email: string;\n      firstName: string;\n      lastName: string;\n      fullName: string;\n      role: UserRole;\n      status: UserStatus;\n      gymId?: string;\n      gymIds?: string[];\n      avatar?: string;\n    };\n    gym?: {\n      id: string;\n      name: string;\n      subdomain: string;\n      domain: string;\n    };\n  }\n}\n\ndeclare module 'next-auth/jwt' {\n  interface JWT {\n    id: string;\n    role: UserRole;\n    status: UserStatus;\n    gymId?: string;\n    gymIds?: string[];\n    gymSubdomain?: string;\n  }\n}\n\nexport const authOptions: NextAuthOptions = {\n  adapter: MongoDBAdapter(\n    connectToDatabase().then(({ db }) => db)\n  ),\n  \n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' },\n        gymSubdomain: { label: 'Gym Subdomain', type: 'text' }\n      },\n      async authorize(credentials) {\n        try {\n          if (!credentials?.email || !credentials?.password) {\n            throw new Error('Email and password are required');\n          }\n\n          // Validate input\n          const validatedCredentials = loginSchema.parse(credentials);\n          \n          // Connect to database\n          await connectToDatabase();\n\n          // Find user by email\n          const user = await User.findOne({ \n            email: validatedCredentials.email.toLowerCase() \n          }).select('+password');\n\n          if (!user) {\n            throw new Error('Invalid email or password');\n          }\n\n          // Check if user is locked\n          if (user.isLocked) {\n            throw new Error('Account is temporarily locked due to too many failed login attempts');\n          }\n\n          // Check if user status allows login\n          if (user.status === UserStatus.SUSPENDED) {\n            throw new Error('Account is suspended');\n          }\n\n          if (user.status === UserStatus.INACTIVE) {\n            throw new Error('Account is inactive');\n          }\n\n          // Verify password\n          const isPasswordValid = await user.comparePassword(validatedCredentials.password);\n          \n          if (!isPasswordValid) {\n            // Increment login attempts\n            await user.incrementLoginAttempts();\n            throw new Error('Invalid email or password');\n          }\n\n          // Reset login attempts on successful login\n          await user.resetLoginAttempts();\n\n          // Handle gym-specific login\n          let selectedGymId = user.gymId;\n          \n          if (validatedCredentials.gymSubdomain) {\n            // Verify user has access to the specified gym\n            const gym = await Gym.findOne({ subdomain: validatedCredentials.gymSubdomain });\n            \n            if (!gym) {\n              throw new Error('Gym not found');\n            }\n\n            // Check if user has access to this gym\n            const hasAccess = user.role === UserRole.SUPER_ADMIN || \n                             user.gymId === gym._id.toString() ||\n                             (user.gymIds && user.gymIds.includes(gym._id.toString()));\n\n            if (!hasAccess) {\n              throw new Error('You do not have access to this gym');\n            }\n\n            selectedGymId = gym._id.toString();\n          }\n\n          // Return user object for NextAuth\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            role: user.role,\n            status: user.status,\n            gymId: selectedGymId,\n            gymIds: user.gymIds,\n            avatar: user.avatar\n          };\n\n        } catch (error) {\n          console.error('Authentication error:', error);\n          throw new Error(error instanceof Error ? error.message : 'Authentication failed');\n        }\n      }\n    })\n  ],\n\n  session: {\n    strategy: 'jwt',\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n\n  jwt: {\n    maxAge: 24 * 60 * 60, // 24 hours\n  },\n\n  callbacks: {\n    async jwt({ token, user, account }) {\n      // Initial sign in\n      if (account && user) {\n        token.id = user.id;\n        token.role = user.role;\n        token.status = user.status;\n        token.gymId = user.gymId;\n        token.gymIds = user.gymIds;\n      }\n\n      return token;\n    },\n\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id;\n        session.user.role = token.role;\n        session.user.status = token.status;\n        session.user.gymId = token.gymId;\n        session.user.gymIds = token.gymIds;\n        session.user.fullName = `${session.user.firstName} ${session.user.lastName}`;\n\n        // Add gym information to session if user has a selected gym\n        if (token.gymId) {\n          try {\n            await connectToDatabase();\n            const gym = await Gym.findById(token.gymId).select('name subdomain domain');\n            \n            if (gym) {\n              session.gym = {\n                id: gym._id.toString(),\n                name: gym.name,\n                subdomain: gym.subdomain,\n                domain: gym.domain\n              };\n            }\n          } catch (error) {\n            console.error('Error fetching gym for session:', error);\n          }\n        }\n      }\n\n      return session;\n    },\n\n    async redirect({ url, baseUrl }) {\n      // Allows relative callback URLs\n      if (url.startsWith('/')) return `${baseUrl}${url}`;\n      \n      // Allows callback URLs on the same origin\n      if (new URL(url).origin === baseUrl) return url;\n      \n      return baseUrl;\n    }\n  },\n\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup',\n    error: '/auth/error',\n    verifyRequest: '/auth/verify-request',\n    newUser: '/auth/welcome'\n  },\n\n  events: {\n    async signIn({ user, account, profile, isNewUser }) {\n      console.log('User signed in:', { \n        userId: user.id, \n        email: user.email, \n        role: user.role,\n        gymId: user.gymId \n      });\n    },\n\n    async signOut({ session, token }) {\n      console.log('User signed out:', { \n        userId: token?.id || session?.user?.id \n      });\n    }\n  },\n\n  debug: process.env.NODE_ENV === 'development',\n};\n\nexport default authOptions;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;AAEA,0BAA0B;AAC1B,MAAM,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,cAAc,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACnC;AAiDO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,uJAAA,CAAA,iBAAc,AAAD,EACpB,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK;IAGvC,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;gBAChD,cAAc;oBAAE,OAAO;oBAAiB,MAAM;gBAAO;YACvD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI;oBACF,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;wBACjD,MAAM,IAAI,MAAM;oBAClB;oBAEA,iBAAiB;oBACjB,MAAM,uBAAuB,YAAY,KAAK,CAAC;oBAE/C,sBAAsB;oBACtB,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;oBAEtB,qBAAqB;oBACrB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,qBAAqB,KAAK,CAAC,WAAW;oBAC/C,GAAG,MAAM,CAAC;oBAEV,IAAI,CAAC,MAAM;wBACT,MAAM,IAAI,MAAM;oBAClB;oBAEA,0BAA0B;oBAC1B,IAAI,KAAK,QAAQ,EAAE;wBACjB,MAAM,IAAI,MAAM;oBAClB;oBAEA,oCAAoC;oBACpC,IAAI,KAAK,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,SAAS,EAAE;wBACxC,MAAM,IAAI,MAAM;oBAClB;oBAEA,IAAI,KAAK,MAAM,KAAK,uHAAA,CAAA,aAAU,CAAC,QAAQ,EAAE;wBACvC,MAAM,IAAI,MAAM;oBAClB;oBAEA,kBAAkB;oBAClB,MAAM,kBAAkB,MAAM,KAAK,eAAe,CAAC,qBAAqB,QAAQ;oBAEhF,IAAI,CAAC,iBAAiB;wBACpB,2BAA2B;wBAC3B,MAAM,KAAK,sBAAsB;wBACjC,MAAM,IAAI,MAAM;oBAClB;oBAEA,2CAA2C;oBAC3C,MAAM,KAAK,kBAAkB;oBAE7B,4BAA4B;oBAC5B,IAAI,gBAAgB,KAAK,KAAK;oBAE9B,IAAI,qBAAqB,YAAY,EAAE;wBACrC,8CAA8C;wBAC9C,MAAM,MAAM,MAAM,sHAAA,CAAA,UAAG,CAAC,OAAO,CAAC;4BAAE,WAAW,qBAAqB,YAAY;wBAAC;wBAE7E,IAAI,CAAC,KAAK;4BACR,MAAM,IAAI,MAAM;wBAClB;wBAEA,uCAAuC;wBACvC,MAAM,YAAY,KAAK,IAAI,KAAK,uHAAA,CAAA,WAAQ,CAAC,WAAW,IACnC,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC,QAAQ,MAC9B,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ;wBAEtE,IAAI,CAAC,WAAW;4BACd,MAAM,IAAI,MAAM;wBAClB;wBAEA,gBAAgB,IAAI,GAAG,CAAC,QAAQ;oBAClC;oBAEA,kCAAkC;oBAClC,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;wBACvB,MAAM,KAAK,IAAI;wBACf,QAAQ,KAAK,MAAM;wBACnB,OAAO;wBACP,QAAQ,KAAK,MAAM;wBACnB,QAAQ,KAAK,MAAM;oBACrB;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3D;YACF;QACF;KACD;IAED,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IAEA,KAAK;QACH,QAAQ,KAAK,KAAK;IACpB;IAEA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,kBAAkB;YAClB,IAAI,WAAW,MAAM;gBACnB,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,MAAM,GAAG,KAAK,MAAM;gBAC1B,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YAEA,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAClC,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;gBAClC,QAAQ,IAAI,CAAC,QAAQ,GAAG,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBAE5E,4DAA4D;gBAC5D,IAAI,MAAM,KAAK,EAAE;oBACf,IAAI;wBACF,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;wBACtB,MAAM,MAAM,MAAM,sHAAA,CAAA,UAAG,CAAC,QAAQ,CAAC,MAAM,KAAK,EAAE,MAAM,CAAC;wBAEnD,IAAI,KAAK;4BACP,QAAQ,GAAG,GAAG;gCACZ,IAAI,IAAI,GAAG,CAAC,QAAQ;gCACpB,MAAM,IAAI,IAAI;gCACd,WAAW,IAAI,SAAS;gCACxB,QAAQ,IAAI,MAAM;4BACpB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF;YACF;YAEA,OAAO;QACT;QAEA,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAE;YAC7B,gCAAgC;YAChC,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAElD,0CAA0C;YAC1C,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAE5C,OAAO;QACT;IACF;IAEA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,eAAe;QACf,SAAS;IACX;IAEA,QAAQ;QACN,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE;YAChD,QAAQ,GAAG,CAAC,mBAAmB;gBAC7B,QAAQ,KAAK,EAAE;gBACf,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;YACnB;QACF;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,QAAQ,GAAG,CAAC,oBAAoB;gBAC9B,QAAQ,OAAO,MAAM,SAAS,MAAM;YACtC;QACF;IACF;IAEA,OAAO,oDAAyB;AAClC;uCAEe", "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { authOptions } from '@/lib/auth/config';\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE,8HAAA,CAAA,cAAW", "debugId": null}}]}
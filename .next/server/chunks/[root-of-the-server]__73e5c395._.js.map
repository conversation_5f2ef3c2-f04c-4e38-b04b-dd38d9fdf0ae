{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\ninterface ConnectionCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongooseCache: ConnectionCache | undefined;\n}\n\nlet cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};\n\nif (!global.mongooseCache) {\n  global.mongooseCache = cached;\n}\n\nexport async function connectToDatabase(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4,\n    };\n\n    const mongoUri = process.env.MONGODB_URI;\n    if (!mongoUri) {\n      throw new Error('MONGODB_URI environment variable is not defined');\n    }\n\n    cached.promise = mongoose.connect(mongoUri, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n    console.log('✅ Connected to MongoDB');\n    return cached.conn;\n  } catch (error) {\n    cached.promise = null;\n    console.error('❌ MongoDB connection error:', error);\n    throw error;\n  }\n}\n\nexport async function disconnectFromDatabase(): Promise<void> {\n  if (cached.conn) {\n    await cached.conn.disconnect();\n    cached.conn = null;\n    cached.promise = null;\n    console.log('🔌 Disconnected from MongoDB');\n  }\n}\n\n// Connection event handlers\nmongoose.connection.on('connected', () => {\n  console.log('🔗 Mongoose connected to MongoDB');\n});\n\nmongoose.connection.on('error', (err) => {\n  console.error('❌ Mongoose connection error:', err);\n});\n\nmongoose.connection.on('disconnected', () => {\n  console.log('🔌 Mongoose disconnected from MongoDB');\n});\n\n// Graceful shutdown (only in Node.js runtime, not Edge Runtime)\n// Note: Removed process handlers to avoid Edge Runtime compatibility issues\n// The connection will be handled by MongoDB driver's built-in connection pooling\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,IAAI,SAA0B,OAAO,aAAa,IAAI;IACpD,MAAM;IACN,SAAS;AACX;AAEA,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG;AACzB;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;IAC9C;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;AACF;AAEA,4BAA4B;AAC5B,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa;IAClC,QAAQ,GAAG,CAAC;AACd;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;IAC/B,QAAQ,KAAK,CAAC,gCAAgC;AAChD;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB;IACrC,QAAQ,GAAG,CAAC;AACd,IAEA,gEAAgE;CAChE,4EAA4E;CAC5E,iFAAiF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/app/api/health/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { connectToDatabase } from '@/lib/database/connection';\n\nexport async function GET() {\n  try {\n    // Test database connection\n    await connectToDatabase();\n    \n    return NextResponse.json({\n      status: 'healthy',\n      timestamp: new Date().toISOString(),\n      database: 'connected',\n      version: '1.0.0',\n    });\n  } catch (error) {\n    console.error('Health check failed:', error);\n    \n    return NextResponse.json(\n      {\n        status: 'unhealthy',\n        timestamp: new Date().toISOString(),\n        database: 'disconnected',\n        error: error instanceof Error ? error.message : 'Unknown error',\n      },\n      { status: 503 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,2BAA2B;QAC3B,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;QAEtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU;YACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
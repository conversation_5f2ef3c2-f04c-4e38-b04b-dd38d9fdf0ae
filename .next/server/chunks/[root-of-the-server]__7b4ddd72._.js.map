{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/lib/database/connection.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\ninterface ConnectionCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongooseCache: ConnectionCache | undefined;\n}\n\nlet cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};\n\nif (!global.mongooseCache) {\n  global.mongooseCache = cached;\n}\n\nexport async function connectToDatabase(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4,\n    };\n\n    const mongoUri = process.env.MONGODB_URI;\n    if (!mongoUri) {\n      throw new Error('MONGODB_URI environment variable is not defined');\n    }\n\n    cached.promise = mongoose.connect(mongoUri, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n    console.log('✅ Connected to MongoDB');\n    return cached.conn;\n  } catch (error) {\n    cached.promise = null;\n    console.error('❌ MongoDB connection error:', error);\n    throw error;\n  }\n}\n\nexport async function disconnectFromDatabase(): Promise<void> {\n  if (cached.conn) {\n    await cached.conn.disconnect();\n    cached.conn = null;\n    cached.promise = null;\n    console.log('🔌 Disconnected from MongoDB');\n  }\n}\n\n// Connection event handlers\nmongoose.connection.on('connected', () => {\n  console.log('🔗 Mongoose connected to MongoDB');\n});\n\nmongoose.connection.on('error', (err) => {\n  console.error('❌ Mongoose connection error:', err);\n});\n\nmongoose.connection.on('disconnected', () => {\n  console.log('🔌 Mongoose disconnected from MongoDB');\n});\n\n// Graceful shutdown (only in Node.js runtime, not Edge Runtime)\n// Note: Removed process handlers to avoid Edge Runtime compatibility issues\n// The connection will be handled by MongoDB driver's built-in connection pooling\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,IAAI,SAA0B,OAAO,aAAa,IAAI;IACpD,MAAM;IACN,SAAS;AACX;AAEA,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG;AACzB;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;IAC9C;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;AACF;AAEA,4BAA4B;AAC5B,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa;IAClC,QAAQ,GAAG,CAAC;AACd;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;IAC/B,QAAQ,KAAK,CAAC,gCAAgC;AAChD;AAEA,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB;IACrC,QAAQ,GAAG,CAAC;AACd,IAEA,gEAAgE;CAChE,4EAA4E;CAC5E,iFAAiF", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/models/Gym.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IGym extends Document {\n  _id: string;\n  name: string;\n  subdomain: string;\n  domain: string;\n  logo?: string;\n  description?: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    zipCode: string;\n    country: string;\n  };\n  contact: {\n    phone: string;\n    email: string;\n    website?: string;\n  };\n  settings: {\n    timezone: string;\n    currency: string;\n    language: string;\n    dateFormat: string;\n    businessHours: {\n      [key: string]: {\n        open: string;\n        close: string;\n        isOpen: boolean;\n      };\n    };\n  };\n  subscription: {\n    plan: 'free' | 'basic' | 'premium' | 'enterprise';\n    status: 'active' | 'inactive' | 'suspended' | 'cancelled';\n    startDate: Date;\n    endDate?: Date;\n    maxMembers: number;\n    maxTrainers: number;\n  };\n  whatsapp: {\n    enabled: boolean;\n    accountSid?: string;\n    authToken?: string;\n    phoneNumber?: string;\n  };\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst GymSchema = new Schema<IGym>({\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: 100,\n  },\n  subdomain: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: /^[a-z0-9-]+$/,\n    maxlength: 50,\n  },\n  domain: {\n    type: String,\n    trim: true,\n  },\n  logo: {\n    type: String,\n    trim: true,\n  },\n  description: {\n    type: String,\n    trim: true,\n    maxlength: 500,\n  },\n  address: {\n    street: { type: String, required: true, trim: true },\n    city: { type: String, required: true, trim: true },\n    state: { type: String, required: true, trim: true },\n    zipCode: { type: String, required: true, trim: true },\n    country: { type: String, required: true, trim: true, default: 'US' },\n  },\n  contact: {\n    phone: { type: String, required: true, trim: true },\n    email: { type: String, required: true, trim: true, lowercase: true },\n    website: { type: String, trim: true },\n  },\n  settings: {\n    timezone: { type: String, default: 'America/New_York' },\n    currency: { type: String, default: 'USD' },\n    language: { type: String, default: 'en' },\n    dateFormat: { type: String, default: 'MM/DD/YYYY' },\n    businessHours: {\n      type: Map,\n      of: {\n        open: { type: String, default: '06:00' },\n        close: { type: String, default: '22:00' },\n        isOpen: { type: Boolean, default: true },\n      },\n      default: {\n        monday: { open: '06:00', close: '22:00', isOpen: true },\n        tuesday: { open: '06:00', close: '22:00', isOpen: true },\n        wednesday: { open: '06:00', close: '22:00', isOpen: true },\n        thursday: { open: '06:00', close: '22:00', isOpen: true },\n        friday: { open: '06:00', close: '22:00', isOpen: true },\n        saturday: { open: '08:00', close: '20:00', isOpen: true },\n        sunday: { open: '08:00', close: '20:00', isOpen: true },\n      },\n    },\n  },\n  subscription: {\n    plan: {\n      type: String,\n      enum: ['free', 'basic', 'premium', 'enterprise'],\n      default: 'free',\n    },\n    status: {\n      type: String,\n      enum: ['active', 'inactive', 'suspended', 'cancelled'],\n      default: 'active',\n    },\n    startDate: { type: Date, default: Date.now },\n    endDate: Date,\n    maxMembers: { type: Number, default: 50 },\n    maxTrainers: { type: Number, default: 5 },\n  },\n  whatsapp: {\n    enabled: { type: Boolean, default: false },\n    accountSid: String,\n    authToken: String,\n    phoneNumber: String,\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true },\n});\n\n// Indexes\nGymSchema.index({ subdomain: 1 });\nGymSchema.index({ domain: 1 });\nGymSchema.index({ isActive: 1 });\nGymSchema.index({ 'subscription.status': 1 });\n\n// Virtual for full address\nGymSchema.virtual('fullAddress').get(function() {\n  return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;\n});\n\n// Pre-save middleware\nGymSchema.pre('save', function(next) {\n  if (this.isModified('subdomain') || this.isNew) {\n    this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;\n  }\n  next();\n});\n\nexport default mongoose.models.Gym || mongoose.model<IGym>('Gym', GymSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqDA,MAAM,YAAY,IAAI,yGAAA,CAAA,SAAM,CAAO;IACjC,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;IACb;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACnD,MAAM;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACjD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACpD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,SAAS;QAAK;IACrE;IACA,SAAS;QACP,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,WAAW;QAAK;QACnE,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;IACtC;IACA,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAmB;QACtD,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAa;QAClD,eAAe;YACb,MAAM;YACN,IAAI;gBACF,MAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACvC,OAAO;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACxC,QAAQ;oBAAE,MAAM;oBAAS,SAAS;gBAAK;YACzC;YACA,SAAS;gBACP,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACvD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACzD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;YACxD;QACF;IACF;IACA,cAAc;QACZ,MAAM;YACJ,MAAM;YACN,MAAM;gBAAC;gBAAQ;gBAAS;gBAAW;aAAa;YAChD,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,MAAM;gBAAC;gBAAU;gBAAY;gBAAa;aAAY;YACtD,SAAS;QACX;QACA,WAAW;YAAE,MAAM;YAAM,SAAS,KAAK,GAAG;QAAC;QAC3C,SAAS;QACT,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAG;QACxC,aAAa;YAAE,MAAM;YAAQ,SAAS;QAAE;IAC1C;IACA,UAAU;QACR,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,UAAU,KAAK,CAAC;IAAE,WAAW;AAAE;AAC/B,UAAU,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC5B,UAAU,KAAK,CAAC;IAAE,UAAU;AAAE;AAC9B,UAAU,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAE3C,2BAA2B;AAC3B,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;IACnC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC/H;AAEA,sBAAsB;AACtB,UAAU,GAAG,CAAC,QAAQ,SAAS,IAAI;IACjC,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,CAAC,KAAK,EAAE;QAC9C,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,UAAU,IAAI,kBAAkB;IACjF;IACA;AACF;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAO,OAAO", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/gymd/src/app/api/tenant/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { connectToDatabase } from '@/lib/database/connection';\nimport Gym from '@/models/Gym';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Get tenant information from headers (set by middleware)\n    const tenantSubdomain = request.headers.get('x-tenant-subdomain') || 'default';\n\n    // If it's the default tenant, return it directly\n    if (tenantSubdomain === 'default') {\n      return NextResponse.json({\n        tenant: {\n          id: 'default',\n          subdomain: 'default',\n          name: 'GymD Platform',\n        },\n        timestamp: new Date().toISOString(),\n      });\n    }\n\n    // For specific gym subdomains, look up in database\n    try {\n      await connectToDatabase();\n\n      const gym = await Gym.findOne({\n        subdomain: tenantSubdomain,\n        isActive: true\n      }).select('_id name subdomain domain isActive').lean() as any;\n\n      if (!gym) {\n        // Return default tenant if gym not found\n        return NextResponse.json({\n          tenant: {\n            id: 'default',\n            subdomain: 'default',\n            name: 'GymD Platform',\n          },\n          timestamp: new Date().toISOString(),\n        });\n      }\n\n      return NextResponse.json({\n        tenant: {\n          id: gym._id.toString(),\n          subdomain: gym.subdomain,\n          name: gym.name,\n        },\n        timestamp: new Date().toISOString(),\n      });\n    } catch (dbError) {\n      console.error('Database error in tenant API:', dbError);\n\n      // Fallback to default tenant on database error\n      return NextResponse.json({\n        tenant: {\n          id: 'default',\n          subdomain: 'default',\n          name: 'GymD Platform',\n        },\n        timestamp: new Date().toISOString(),\n      });\n    }\n  } catch (error) {\n    console.error('Tenant API error:', error);\n\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,0DAA0D;QAC1D,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC,yBAAyB;QAErE,iDAAiD;QACjD,IAAI,oBAAoB,WAAW;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;oBACN,IAAI;oBACJ,WAAW;oBACX,MAAM;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,mDAAmD;QACnD,IAAI;YACF,MAAM,CAAA,GAAA,sIAAA,CAAA,oBAAiB,AAAD;YAEtB,MAAM,MAAM,MAAM,sHAAA,CAAA,UAAG,CAAC,OAAO,CAAC;gBAC5B,WAAW;gBACX,UAAU;YACZ,GAAG,MAAM,CAAC,sCAAsC,IAAI;YAEpD,IAAI,CAAC,KAAK;gBACR,yCAAyC;gBACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,MAAM;oBACR;oBACA,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;oBACN,IAAI,IAAI,GAAG,CAAC,QAAQ;oBACpB,WAAW,IAAI,SAAS;oBACxB,MAAM,IAAI,IAAI;gBAChB;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,KAAK,CAAC,iCAAiC;YAE/C,+CAA+C;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,QAAQ;oBACN,IAAI;oBACJ,WAAW;oBACX,MAAM;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}
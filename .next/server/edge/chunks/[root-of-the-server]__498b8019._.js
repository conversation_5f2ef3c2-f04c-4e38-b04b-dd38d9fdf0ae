(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__498b8019._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/database/connection.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "connectToDatabase": (()=>connectToDatabase),
    "disconnectFromDatabase": (()=>disconnectFromDatabase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mongoose/dist/browser.umd.js [middleware-edge] (ecmascript)");
;
let cached = global.mongooseCache || {
    conn: null,
    promise: null
};
if (!global.mongooseCache) {
    global.mongooseCache = cached;
}
async function connectToDatabase() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false,
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            family: 4
        };
        const mongoUri = process.env.MONGODB_URI;
        if (!mongoUri) {
            throw new Error('MONGODB_URI environment variable is not defined');
        }
        cached.promise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].connect(mongoUri, opts);
    }
    try {
        cached.conn = await cached.promise;
        console.log('✅ Connected to MongoDB');
        return cached.conn;
    } catch (error) {
        cached.promise = null;
        console.error('❌ MongoDB connection error:', error);
        throw error;
    }
}
async function disconnectFromDatabase() {
    if (cached.conn) {
        await cached.conn.disconnect();
        cached.conn = null;
        cached.promise = null;
        console.log('🔌 Disconnected from MongoDB');
    }
}
// Connection event handlers
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].connection.on('connected', ()=>{
    console.log('🔗 Mongoose connected to MongoDB');
});
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].connection.on('error', (err)=>{
    console.error('❌ Mongoose connection error:', err);
});
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].connection.on('disconnected', ()=>{
    console.log('🔌 Mongoose disconnected from MongoDB');
});
// Graceful shutdown (only in Node.js runtime, not Edge Runtime)
if (typeof process !== 'undefined' && process.on) {
    process.on('SIGINT', async ()=>{
        await disconnectFromDatabase();
        process.exit(0);
    });
}
}}),
"[project]/src/models/Gym.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mongoose/dist/browser.umd.js [middleware-edge] (ecmascript)");
;
const GymSchema = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Schema"]({
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    subdomain: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
        match: /^[a-z0-9-]+$/,
        maxlength: 50
    },
    domain: {
        type: String,
        trim: true
    },
    logo: {
        type: String,
        trim: true
    },
    description: {
        type: String,
        trim: true,
        maxlength: 500
    },
    address: {
        street: {
            type: String,
            required: true,
            trim: true
        },
        city: {
            type: String,
            required: true,
            trim: true
        },
        state: {
            type: String,
            required: true,
            trim: true
        },
        zipCode: {
            type: String,
            required: true,
            trim: true
        },
        country: {
            type: String,
            required: true,
            trim: true,
            default: 'US'
        }
    },
    contact: {
        phone: {
            type: String,
            required: true,
            trim: true
        },
        email: {
            type: String,
            required: true,
            trim: true,
            lowercase: true
        },
        website: {
            type: String,
            trim: true
        }
    },
    settings: {
        timezone: {
            type: String,
            default: 'America/New_York'
        },
        currency: {
            type: String,
            default: 'USD'
        },
        language: {
            type: String,
            default: 'en'
        },
        dateFormat: {
            type: String,
            default: 'MM/DD/YYYY'
        },
        businessHours: {
            type: Map,
            of: {
                open: {
                    type: String,
                    default: '06:00'
                },
                close: {
                    type: String,
                    default: '22:00'
                },
                isOpen: {
                    type: Boolean,
                    default: true
                }
            },
            default: {
                monday: {
                    open: '06:00',
                    close: '22:00',
                    isOpen: true
                },
                tuesday: {
                    open: '06:00',
                    close: '22:00',
                    isOpen: true
                },
                wednesday: {
                    open: '06:00',
                    close: '22:00',
                    isOpen: true
                },
                thursday: {
                    open: '06:00',
                    close: '22:00',
                    isOpen: true
                },
                friday: {
                    open: '06:00',
                    close: '22:00',
                    isOpen: true
                },
                saturday: {
                    open: '08:00',
                    close: '20:00',
                    isOpen: true
                },
                sunday: {
                    open: '08:00',
                    close: '20:00',
                    isOpen: true
                }
            }
        }
    },
    subscription: {
        plan: {
            type: String,
            enum: [
                'free',
                'basic',
                'premium',
                'enterprise'
            ],
            default: 'free'
        },
        status: {
            type: String,
            enum: [
                'active',
                'inactive',
                'suspended',
                'cancelled'
            ],
            default: 'active'
        },
        startDate: {
            type: Date,
            default: Date.now
        },
        endDate: Date,
        maxMembers: {
            type: Number,
            default: 50
        },
        maxTrainers: {
            type: Number,
            default: 5
        }
    },
    whatsapp: {
        enabled: {
            type: Boolean,
            default: false
        },
        accountSid: String,
        authToken: String,
        phoneNumber: String
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes
GymSchema.index({
    subdomain: 1
});
GymSchema.index({
    domain: 1
});
GymSchema.index({
    isActive: 1
});
GymSchema.index({
    'subscription.status': 1
});
// Virtual for full address
GymSchema.virtual('fullAddress').get(function() {
    return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;
});
// Pre-save middleware
GymSchema.pre('save', function(next) {
    if (this.isModified('subdomain') || this.isNew) {
        this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;
    }
    next();
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].models.Gym || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"].model('Gym', GymSchema);
}}),
"[project]/src/lib/tenant/resolver.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TenantResolver": (()=>TenantResolver),
    "tenantResolver": (()=>tenantResolver)
});
class TenantResolver {
    static instance;
    tenantCache = new Map();
    constructor(){}
    static getInstance() {
        if (!TenantResolver.instance) {
            TenantResolver.instance = new TenantResolver();
        }
        return TenantResolver.instance;
    }
    /**
   * Resolve tenant from request
   */ async resolveTenant(request) {
        const host = request.headers.get('host') || '';
        const resolutionMode = process.env.TENANT_RESOLUTION_MODE || 'subdomain';
        if (resolutionMode === 'subdomain') {
            return this.resolveFromSubdomain(host);
        } else {
            return this.resolveFromPath(request.nextUrl.pathname);
        }
    }
    /**
   * Resolve tenant from subdomain (e.g., gym1.yourdomain.com)
   */ async resolveFromSubdomain(host) {
        const parts = host.split('.');
        // For localhost development
        if (host.includes('localhost')) {
            // Handle localhost with port (e.g., localhost:3001)
            const subdomain = parts[0].split(':')[0]; // Remove port from subdomain
            if (subdomain === 'localhost') {
                return this.getDefaultTenant();
            }
            return this.getTenantBySubdomain(subdomain);
        }
        // For production domains
        if (parts.length >= 3) {
            const subdomain = parts[0];
            return this.getTenantBySubdomain(subdomain);
        }
        return this.getDefaultTenant();
    }
    /**
   * Resolve tenant from URL path (e.g., /gym1/dashboard)
   */ async resolveFromPath(pathname) {
        const pathParts = pathname.split('/').filter(Boolean);
        if (pathParts.length > 0) {
            const potentialTenant = pathParts[0];
            return this.getTenantBySubdomain(potentialTenant);
        }
        return this.getDefaultTenant();
    }
    /**
   * Get tenant by subdomain from cache or database
   */ async getTenantBySubdomain(subdomain) {
        // Check cache first
        if (this.tenantCache.has(subdomain)) {
            return this.tenantCache.get(subdomain) || null;
        }
        // Query the database for the tenant
        try {
            // Import here to avoid circular dependencies
            const { connectToDatabase } = await Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/lib/database/connection.ts [middleware-edge] (ecmascript)"));
            const Gym = (await Promise.resolve().then(()=>__turbopack_context__.i("[project]/src/models/Gym.ts [middleware-edge] (ecmascript)"))).default;
            await connectToDatabase();
            const gym = await Gym.findOne({
                subdomain: subdomain,
                isActive: true
            }).select('_id name subdomain domain isActive').lean();
            if (!gym) {
                return null;
            }
            const tenant = {
                id: gym._id.toString(),
                subdomain: gym.subdomain,
                domain: gym.domain,
                name: gym.name,
                isActive: gym.isActive
            };
            // Cache the result
            this.tenantCache.set(subdomain, tenant);
            return tenant;
        } catch (error) {
            console.error('Error fetching tenant from database:', error);
            return null;
        }
    }
    /**
   * Get default tenant for main domain
   */ getDefaultTenant() {
        return {
            id: 'default',
            subdomain: 'www',
            domain: 'localhost:3000',
            name: 'GymD Platform',
            isActive: true
        };
    }
    /**
   * Clear tenant cache
   */ clearCache() {
        this.tenantCache.clear();
    }
    /**
   * Get tenant by ID
   */ async getTenantById(tenantId) {
        // In a real implementation, this would query the database
        // For now, search through cached tenants
        for (const tenant of this.tenantCache.values()){
            if (tenant.id === tenantId) {
                return tenant;
            }
        }
        return null;
    }
    /**
   * Validate tenant access
   */ validateTenantAccess(tenant) {
        return tenant.isActive;
    }
}
const tenantResolver = TenantResolver.getInstance();
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$tenant$2f$resolver$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/tenant/resolver.ts [middleware-edge] (ecmascript)");
;
;
async function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip middleware for static files, error pages, and API routes that don't need tenant context
    if (pathname.startsWith('/_next/') || pathname.startsWith('/api/health') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.startsWith('/tenant-not-found') || pathname.startsWith('/tenant-inactive') || pathname.startsWith('/error') || pathname.match(/\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    try {
        // Resolve tenant from request
        const tenant = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$tenant$2f$resolver$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["tenantResolver"].resolveTenant(request);
        if (!tenant) {
            // Redirect to tenant selection or error page
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/tenant-not-found', request.url));
        }
        // Validate tenant access
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$tenant$2f$resolver$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["tenantResolver"].validateTenantAccess(tenant)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/tenant-inactive', request.url));
        }
        // Clone the request headers and add tenant information
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set('x-tenant-id', tenant.id);
        requestHeaders.set('x-tenant-subdomain', tenant.subdomain);
        requestHeaders.set('x-tenant-name', tenant.name);
        // Create response with tenant headers
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
            request: {
                headers: requestHeaders
            }
        });
        // Add tenant info to response headers for client-side access
        response.headers.set('x-tenant-id', tenant.id);
        response.headers.set('x-tenant-name', tenant.name);
        return response;
    } catch (error) {
        console.error('Middleware error:', error);
        // In case of error, redirect to error page
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/error', request.url));
    }
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * But include API routes that need tenant context
     */ '/((?!_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__498b8019._.js.map
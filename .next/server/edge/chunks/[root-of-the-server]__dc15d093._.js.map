{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Skip middleware for static files, error pages, and API routes that don't need tenant context\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/api/health') ||\n    pathname.startsWith('/favicon.ico') ||\n    pathname.startsWith('/public/') ||\n    pathname.startsWith('/tenant-not-found') ||\n    pathname.startsWith('/tenant-inactive') ||\n    pathname.startsWith('/error') ||\n    pathname.match(/\\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/)\n  ) {\n    return NextResponse.next();\n  }\n\n  try {\n    // Simple tenant resolution for Edge Runtime\n    const host = request.headers.get('host') || '';\n    const parts = host.split('.');\n\n    let tenantSubdomain = 'default';\n    let tenantName = 'GymD Platform';\n\n    // For localhost development\n    if (host.includes('localhost')) {\n      const subdomain = parts[0].split(':')[0]; // Remove port from subdomain\n      if (subdomain !== 'localhost') {\n        tenantSubdomain = subdomain;\n        tenantName = `${subdomain} Gym`;\n      }\n    } else if (parts.length >= 3) {\n      // For production domains\n      tenantSubdomain = parts[0];\n      tenantName = `${parts[0]} Gym`;\n    }\n\n    // Clone the request headers and add tenant information\n    const requestHeaders = new Headers(request.headers);\n    requestHeaders.set('x-tenant-subdomain', tenantSubdomain);\n    requestHeaders.set('x-tenant-name', tenantName);\n\n    // Create response with tenant headers\n    const response = NextResponse.next({\n      request: {\n        headers: requestHeaders,\n      },\n    });\n\n    // Add tenant info to response headers for client-side access\n    response.headers.set('x-tenant-subdomain', tenantSubdomain);\n    response.headers.set('x-tenant-name', tenantName);\n\n    return response;\n  } catch (error) {\n    console.error('Middleware error:', error);\n    return NextResponse.next();\n  }\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * But include API routes that need tenant context\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,+FAA+F;IAC/F,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC,wBACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,aACpB,SAAS,KAAK,CAAC,4DACf;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,IAAI;QACF,4CAA4C;QAC5C,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;QAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,IAAI,kBAAkB;QACtB,IAAI,aAAa;QAEjB,4BAA4B;QAC5B,IAAI,KAAK,QAAQ,CAAC,cAAc;YAC9B,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;YACvE,IAAI,cAAc,aAAa;gBAC7B,kBAAkB;gBAClB,aAAa,GAAG,UAAU,IAAI,CAAC;YACjC;QACF,OAAO,IAAI,MAAM,MAAM,IAAI,GAAG;YAC5B,yBAAyB;YACzB,kBAAkB,KAAK,CAAC,EAAE;YAC1B,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;QAChC;QAEA,uDAAuD;QACvD,MAAM,iBAAiB,IAAI,QAAQ,QAAQ,OAAO;QAClD,eAAe,GAAG,CAAC,sBAAsB;QACzC,eAAe,GAAG,CAAC,iBAAiB;QAEpC,sCAAsC;QACtC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;gBACP,SAAS;YACX;QACF;QAEA,6DAA6D;QAC7D,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;QAC3C,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAEtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}
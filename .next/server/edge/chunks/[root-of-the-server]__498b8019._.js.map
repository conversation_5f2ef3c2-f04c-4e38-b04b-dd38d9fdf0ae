{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/database/connection.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\ninterface ConnectionCache {\n  conn: typeof mongoose | null;\n  promise: Promise<typeof mongoose> | null;\n}\n\ndeclare global {\n  var mongooseCache: ConnectionCache | undefined;\n}\n\nlet cached: ConnectionCache = global.mongooseCache || {\n  conn: null,\n  promise: null,\n};\n\nif (!global.mongooseCache) {\n  global.mongooseCache = cached;\n}\n\nexport async function connectToDatabase(): Promise<typeof mongoose> {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n      maxPoolSize: 10,\n      serverSelectionTimeoutMS: 5000,\n      socketTimeoutMS: 45000,\n      family: 4,\n    };\n\n    const mongoUri = process.env.MONGODB_URI;\n    if (!mongoUri) {\n      throw new Error('MONGODB_URI environment variable is not defined');\n    }\n\n    cached.promise = mongoose.connect(mongoUri, opts);\n  }\n\n  try {\n    cached.conn = await cached.promise;\n    console.log('✅ Connected to MongoDB');\n    return cached.conn;\n  } catch (error) {\n    cached.promise = null;\n    console.error('❌ MongoDB connection error:', error);\n    throw error;\n  }\n}\n\nexport async function disconnectFromDatabase(): Promise<void> {\n  if (cached.conn) {\n    await cached.conn.disconnect();\n    cached.conn = null;\n    cached.promise = null;\n    console.log('🔌 Disconnected from MongoDB');\n  }\n}\n\n// Connection event handlers\nmongoose.connection.on('connected', () => {\n  console.log('🔗 Mongoose connected to MongoDB');\n});\n\nmongoose.connection.on('error', (err) => {\n  console.error('❌ Mongoose connection error:', err);\n});\n\nmongoose.connection.on('disconnected', () => {\n  console.log('🔌 Mongoose disconnected from MongoDB');\n});\n\n// Graceful shutdown (only in Node.js runtime, not Edge Runtime)\nif (typeof process !== 'undefined' && process.on) {\n  process.on('SIGINT', async () => {\n    await disconnectFromDatabase();\n    process.exit(0);\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAWA,IAAI,SAA0B,OAAO,aAAa,IAAI;IACpD,MAAM;IACN,SAAS;AACX;AAEA,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG;AACzB;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;YAChB,aAAa;YACb,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QACxC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,OAAO,GAAG,0JAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;IAC9C;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;QAClC,QAAQ,GAAG,CAAC;QACZ,OAAO,OAAO,IAAI;IACpB,EAAE,OAAO,OAAO;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe;IACpB,IAAI,OAAO,IAAI,EAAE;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,QAAQ,GAAG,CAAC;IACd;AACF;AAEA,4BAA4B;AAC5B,0JAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa;IAClC,QAAQ,GAAG,CAAC;AACd;AAEA,0JAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;IAC/B,QAAQ,KAAK,CAAC,gCAAgC;AAChD;AAEA,0JAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAgB;IACrC,QAAQ,GAAG,CAAC;AACd;AAEA,gEAAgE;AAChE,IAAI,OAAO,YAAY,eAAe,QAAQ,EAAE,EAAE;IAChD,QAAQ,EAAE,CAAC,UAAU;QACnB,MAAM;QACN,QAAQ,IAAI,CAAC;IACf;AACF"}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/models/Gym.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IGym extends Document {\n  _id: string;\n  name: string;\n  subdomain: string;\n  domain: string;\n  logo?: string;\n  description?: string;\n  address: {\n    street: string;\n    city: string;\n    state: string;\n    zipCode: string;\n    country: string;\n  };\n  contact: {\n    phone: string;\n    email: string;\n    website?: string;\n  };\n  settings: {\n    timezone: string;\n    currency: string;\n    language: string;\n    dateFormat: string;\n    businessHours: {\n      [key: string]: {\n        open: string;\n        close: string;\n        isOpen: boolean;\n      };\n    };\n  };\n  subscription: {\n    plan: 'free' | 'basic' | 'premium' | 'enterprise';\n    status: 'active' | 'inactive' | 'suspended' | 'cancelled';\n    startDate: Date;\n    endDate?: Date;\n    maxMembers: number;\n    maxTrainers: number;\n  };\n  whatsapp: {\n    enabled: boolean;\n    accountSid?: string;\n    authToken?: string;\n    phoneNumber?: string;\n  };\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst GymSchema = new Schema<IGym>({\n  name: {\n    type: String,\n    required: true,\n    trim: true,\n    maxlength: 100,\n  },\n  subdomain: {\n    type: String,\n    required: true,\n    unique: true,\n    lowercase: true,\n    trim: true,\n    match: /^[a-z0-9-]+$/,\n    maxlength: 50,\n  },\n  domain: {\n    type: String,\n    trim: true,\n  },\n  logo: {\n    type: String,\n    trim: true,\n  },\n  description: {\n    type: String,\n    trim: true,\n    maxlength: 500,\n  },\n  address: {\n    street: { type: String, required: true, trim: true },\n    city: { type: String, required: true, trim: true },\n    state: { type: String, required: true, trim: true },\n    zipCode: { type: String, required: true, trim: true },\n    country: { type: String, required: true, trim: true, default: 'US' },\n  },\n  contact: {\n    phone: { type: String, required: true, trim: true },\n    email: { type: String, required: true, trim: true, lowercase: true },\n    website: { type: String, trim: true },\n  },\n  settings: {\n    timezone: { type: String, default: 'America/New_York' },\n    currency: { type: String, default: 'USD' },\n    language: { type: String, default: 'en' },\n    dateFormat: { type: String, default: 'MM/DD/YYYY' },\n    businessHours: {\n      type: Map,\n      of: {\n        open: { type: String, default: '06:00' },\n        close: { type: String, default: '22:00' },\n        isOpen: { type: Boolean, default: true },\n      },\n      default: {\n        monday: { open: '06:00', close: '22:00', isOpen: true },\n        tuesday: { open: '06:00', close: '22:00', isOpen: true },\n        wednesday: { open: '06:00', close: '22:00', isOpen: true },\n        thursday: { open: '06:00', close: '22:00', isOpen: true },\n        friday: { open: '06:00', close: '22:00', isOpen: true },\n        saturday: { open: '08:00', close: '20:00', isOpen: true },\n        sunday: { open: '08:00', close: '20:00', isOpen: true },\n      },\n    },\n  },\n  subscription: {\n    plan: {\n      type: String,\n      enum: ['free', 'basic', 'premium', 'enterprise'],\n      default: 'free',\n    },\n    status: {\n      type: String,\n      enum: ['active', 'inactive', 'suspended', 'cancelled'],\n      default: 'active',\n    },\n    startDate: { type: Date, default: Date.now },\n    endDate: Date,\n    maxMembers: { type: Number, default: 50 },\n    maxTrainers: { type: Number, default: 5 },\n  },\n  whatsapp: {\n    enabled: { type: Boolean, default: false },\n    accountSid: String,\n    authToken: String,\n    phoneNumber: String,\n  },\n  isActive: {\n    type: Boolean,\n    default: true,\n  },\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true },\n});\n\n// Indexes\nGymSchema.index({ subdomain: 1 });\nGymSchema.index({ domain: 1 });\nGymSchema.index({ isActive: 1 });\nGymSchema.index({ 'subscription.status': 1 });\n\n// Virtual for full address\nGymSchema.virtual('fullAddress').get(function() {\n  return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;\n});\n\n// Pre-save middleware\nGymSchema.pre('save', function(next) {\n  if (this.isModified('subdomain') || this.isNew) {\n    this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;\n  }\n  next();\n});\n\nexport default mongoose.models.Gym || mongoose.model<IGym>('Gym', GymSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAqDA,MAAM,YAAY,IAAI,0JAAA,CAAA,SAAM,CAAO;IACjC,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;QACN,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;IACb;IACA,SAAS;QACP,QAAQ;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACnD,MAAM;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACjD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QACpD,SAAS;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,SAAS;QAAK;IACrE;IACA,SAAS;QACP,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;QAAK;QAClD,OAAO;YAAE,MAAM;YAAQ,UAAU;YAAM,MAAM;YAAM,WAAW;QAAK;QACnE,SAAS;YAAE,MAAM;YAAQ,MAAM;QAAK;IACtC;IACA,UAAU;QACR,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAmB;QACtD,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAM;QACzC,UAAU;YAAE,MAAM;YAAQ,SAAS;QAAK;QACxC,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAa;QAClD,eAAe;YACb,MAAM;YACN,IAAI;gBACF,MAAM;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACvC,OAAO;oBAAE,MAAM;oBAAQ,SAAS;gBAAQ;gBACxC,QAAQ;oBAAE,MAAM;oBAAS,SAAS;gBAAK;YACzC;YACA,SAAS;gBACP,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,SAAS;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACvD,WAAW;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACzD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACtD,UAAU;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;gBACxD,QAAQ;oBAAE,MAAM;oBAAS,OAAO;oBAAS,QAAQ;gBAAK;YACxD;QACF;IACF;IACA,cAAc;QACZ,MAAM;YACJ,MAAM;YACN,MAAM;gBAAC;gBAAQ;gBAAS;gBAAW;aAAa;YAChD,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,MAAM;gBAAC;gBAAU;gBAAY;gBAAa;aAAY;YACtD,SAAS;QACX;QACA,WAAW;YAAE,MAAM;YAAM,SAAS,KAAK,GAAG;QAAC;QAC3C,SAAS;QACT,YAAY;YAAE,MAAM;YAAQ,SAAS;QAAG;QACxC,aAAa;YAAE,MAAM;YAAQ,SAAS;QAAE;IAC1C;IACA,UAAU;QACR,SAAS;YAAE,MAAM;YAAS,SAAS;QAAM;QACzC,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,UAAU,KAAK,CAAC;IAAE,WAAW;AAAE;AAC/B,UAAU,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC5B,UAAU,KAAK,CAAC;IAAE,UAAU;AAAE;AAC9B,UAAU,KAAK,CAAC;IAAE,uBAAuB;AAAE;AAE3C,2BAA2B;AAC3B,UAAU,OAAO,CAAC,eAAe,GAAG,CAAC;IACnC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC/H;AAEA,sBAAsB;AACtB,UAAU,GAAG,CAAC,QAAQ,SAAS,IAAI;IACjC,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,CAAC,KAAK,EAAE;QAC9C,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,UAAU,IAAI,kBAAkB;IACjF;IACA;AACF;uCAEe,0JAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,0JAAA,CAAA,UAAQ,CAAC,KAAK,CAAO,OAAO"}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/tenant/resolver.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport interface TenantInfo {\n  id: string;\n  subdomain: string;\n  domain: string;\n  name: string;\n  isActive: boolean;\n}\n\nexport class TenantResolver {\n  private static instance: TenantResolver;\n  private tenantCache = new Map<string, TenantInfo>();\n\n  private constructor() {}\n\n  public static getInstance(): TenantResolver {\n    if (!TenantResolver.instance) {\n      TenantResolver.instance = new TenantResolver();\n    }\n    return TenantResolver.instance;\n  }\n\n  /**\n   * Resolve tenant from request\n   */\n  public async resolveTenant(request: NextRequest): Promise<TenantInfo | null> {\n    const host = request.headers.get('host') || '';\n    const resolutionMode = process.env.TENANT_RESOLUTION_MODE || 'subdomain';\n\n    if (resolutionMode === 'subdomain') {\n      return this.resolveFromSubdomain(host);\n    } else {\n      return this.resolveFromPath(request.nextUrl.pathname);\n    }\n  }\n\n  /**\n   * Resolve tenant from subdomain (e.g., gym1.yourdomain.com)\n   */\n  private async resolveFromSubdomain(host: string): Promise<TenantInfo | null> {\n    const parts = host.split('.');\n\n    // For localhost development\n    if (host.includes('localhost')) {\n      // Handle localhost with port (e.g., localhost:3001)\n      const subdomain = parts[0].split(':')[0]; // Remove port from subdomain\n\n      if (subdomain === 'localhost') {\n        return this.getDefaultTenant();\n      }\n      return this.getTenantBySubdomain(subdomain);\n    }\n\n    // For production domains\n    if (parts.length >= 3) {\n      const subdomain = parts[0];\n      return this.getTenantBySubdomain(subdomain);\n    }\n\n    return this.getDefaultTenant();\n  }\n\n  /**\n   * Resolve tenant from URL path (e.g., /gym1/dashboard)\n   */\n  private async resolveFromPath(pathname: string): Promise<TenantInfo | null> {\n    const pathParts = pathname.split('/').filter(Boolean);\n    \n    if (pathParts.length > 0) {\n      const potentialTenant = pathParts[0];\n      return this.getTenantBySubdomain(potentialTenant);\n    }\n\n    return this.getDefaultTenant();\n  }\n\n  /**\n   * Get tenant by subdomain from cache or database\n   */\n  private async getTenantBySubdomain(subdomain: string): Promise<TenantInfo | null> {\n    // Check cache first\n    if (this.tenantCache.has(subdomain)) {\n      return this.tenantCache.get(subdomain) || null;\n    }\n\n    // Query the database for the tenant\n    try {\n      // Import here to avoid circular dependencies\n      const { connectToDatabase } = await import('@/lib/database/connection');\n      const Gym = (await import('@/models/Gym')).default;\n\n      await connectToDatabase();\n\n      const gym = await Gym.findOne({\n        subdomain: subdomain,\n        isActive: true\n      }).select('_id name subdomain domain isActive').lean() as any;\n\n      if (!gym) {\n        return null;\n      }\n\n      const tenant: TenantInfo = {\n        id: gym._id.toString(),\n        subdomain: gym.subdomain,\n        domain: gym.domain,\n        name: gym.name,\n        isActive: gym.isActive,\n      };\n\n      // Cache the result\n      this.tenantCache.set(subdomain, tenant);\n      return tenant;\n    } catch (error) {\n      console.error('Error fetching tenant from database:', error);\n      return null;\n    }\n\n  }\n\n  /**\n   * Get default tenant for main domain\n   */\n  private getDefaultTenant(): TenantInfo {\n    return {\n      id: 'default',\n      subdomain: 'www',\n      domain: 'localhost:3000',\n      name: 'GymD Platform',\n      isActive: true,\n    };\n  }\n\n  /**\n   * Clear tenant cache\n   */\n  public clearCache(): void {\n    this.tenantCache.clear();\n  }\n\n  /**\n   * Get tenant by ID\n   */\n  public async getTenantById(tenantId: string): Promise<TenantInfo | null> {\n    // In a real implementation, this would query the database\n    // For now, search through cached tenants\n    for (const tenant of this.tenantCache.values()) {\n      if (tenant.id === tenantId) {\n        return tenant;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Validate tenant access\n   */\n  public validateTenantAccess(tenant: TenantInfo): boolean {\n    return tenant.isActive;\n  }\n}\n\nexport const tenantResolver = TenantResolver.getInstance();\n"], "names": [], "mappings": ";;;;AAUO,MAAM;IACX,OAAe,SAAyB;IAChC,cAAc,IAAI,MAA0B;IAEpD,aAAsB,CAAC;IAEvB,OAAc,cAA8B;QAC1C,IAAI,CAAC,eAAe,QAAQ,EAAE;YAC5B,eAAe,QAAQ,GAAG,IAAI;QAChC;QACA,OAAO,eAAe,QAAQ;IAChC;IAEA;;GAEC,GACD,MAAa,cAAc,OAAoB,EAA8B;QAC3E,MAAM,OAAO,QAAQ,OAAO,CAAC,GAAG,CAAC,WAAW;QAC5C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,sBAAsB,IAAI;QAE7D,IAAI,mBAAmB,aAAa;YAClC,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC,OAAO;YACL,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,OAAO,CAAC,QAAQ;QACtD;IACF;IAEA;;GAEC,GACD,MAAc,qBAAqB,IAAY,EAA8B;QAC3E,MAAM,QAAQ,KAAK,KAAK,CAAC;QAEzB,4BAA4B;QAC5B,IAAI,KAAK,QAAQ,CAAC,cAAc;YAC9B,oDAAoD;YACpD,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,6BAA6B;YAEvE,IAAI,cAAc,aAAa;gBAC7B,OAAO,IAAI,CAAC,gBAAgB;YAC9B;YACA,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;QAEA,yBAAyB;QACzB,IAAI,MAAM,MAAM,IAAI,GAAG;YACrB,MAAM,YAAY,KAAK,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;QAEA,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA;;GAEC,GACD,MAAc,gBAAgB,QAAgB,EAA8B;QAC1E,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAE7C,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,MAAM,kBAAkB,SAAS,CAAC,EAAE;YACpC,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;QAEA,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA;;GAEC,GACD,MAAc,qBAAqB,SAAiB,EAA8B;QAChF,oBAAoB;QACpB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY;YACnC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc;QAC5C;QAEA,oCAAoC;QACpC,IAAI;YACF,6CAA6C;YAC7C,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,MAAM,MAAM,CAAC,uHAA4B,EAAE,OAAO;YAElD,MAAM;YAEN,MAAM,MAAM,MAAM,IAAI,OAAO,CAAC;gBAC5B,WAAW;gBACX,UAAU;YACZ,GAAG,MAAM,CAAC,sCAAsC,IAAI;YAEpD,IAAI,CAAC,KAAK;gBACR,OAAO;YACT;YAEA,MAAM,SAAqB;gBACzB,IAAI,IAAI,GAAG,CAAC,QAAQ;gBACpB,WAAW,IAAI,SAAS;gBACxB,QAAQ,IAAI,MAAM;gBAClB,MAAM,IAAI,IAAI;gBACd,UAAU,IAAI,QAAQ;YACxB;YAEA,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IAEF;IAEA;;GAEC,GACD,AAAQ,mBAA+B;QACrC,OAAO;YACL,IAAI;YACJ,WAAW;YACX,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,CAAC,WAAW,CAAC,KAAK;IACxB;IAEA;;GAEC,GACD,MAAa,cAAc,QAAgB,EAA8B;QACvE,0DAA0D;QAC1D,yCAAyC;QACzC,KAAK,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,GAAI;YAC9C,IAAI,OAAO,EAAE,KAAK,UAAU;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAO,qBAAqB,MAAkB,EAAW;QACvD,OAAO,OAAO,QAAQ;IACxB;AACF;AAEO,MAAM,iBAAiB,eAAe,WAAW"}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\nimport { tenantResolver } from './lib/tenant/resolver';\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n\n  // Skip middleware for static files, error pages, and API routes that don't need tenant context\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/api/health') ||\n    pathname.startsWith('/favicon.ico') ||\n    pathname.startsWith('/public/') ||\n    pathname.startsWith('/tenant-not-found') ||\n    pathname.startsWith('/tenant-inactive') ||\n    pathname.startsWith('/error') ||\n    pathname.match(/\\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/)\n  ) {\n    return NextResponse.next();\n  }\n\n  try {\n    // Resolve tenant from request\n    const tenant = await tenantResolver.resolveTenant(request);\n\n    if (!tenant) {\n      // Redirect to tenant selection or error page\n      return NextResponse.redirect(new URL('/tenant-not-found', request.url));\n    }\n\n    // Validate tenant access\n    if (!tenantResolver.validateTenantAccess(tenant)) {\n      return NextResponse.redirect(new URL('/tenant-inactive', request.url));\n    }\n\n    // Clone the request headers and add tenant information\n    const requestHeaders = new Headers(request.headers);\n    requestHeaders.set('x-tenant-id', tenant.id);\n    requestHeaders.set('x-tenant-subdomain', tenant.subdomain);\n    requestHeaders.set('x-tenant-name', tenant.name);\n\n    // Create response with tenant headers\n    const response = NextResponse.next({\n      request: {\n        headers: requestHeaders,\n      },\n    });\n\n    // Add tenant info to response headers for client-side access\n    response.headers.set('x-tenant-id', tenant.id);\n    response.headers.set('x-tenant-name', tenant.name);\n\n    return response;\n  } catch (error) {\n    console.error('Middleware error:', error);\n    \n    // In case of error, redirect to error page\n    return NextResponse.redirect(new URL('/error', request.url));\n  }\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * But include API routes that need tenant context\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAEA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,+FAA+F;IAC/F,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,mBACpB,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC,wBACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,aACpB,SAAS,KAAK,CAAC,4DACf;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,IAAI;QACF,8BAA8B;QAC9B,MAAM,SAAS,MAAM,wIAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;QAElD,IAAI,CAAC,QAAQ;YACX,6CAA6C;YAC7C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,qBAAqB,QAAQ,GAAG;QACvE;QAEA,yBAAyB;QACzB,IAAI,CAAC,wIAAA,CAAA,iBAAc,CAAC,oBAAoB,CAAC,SAAS;YAChD,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,oBAAoB,QAAQ,GAAG;QACtE;QAEA,uDAAuD;QACvD,MAAM,iBAAiB,IAAI,QAAQ,QAAQ,OAAO;QAClD,eAAe,GAAG,CAAC,eAAe,OAAO,EAAE;QAC3C,eAAe,GAAG,CAAC,sBAAsB,OAAO,SAAS;QACzD,eAAe,GAAG,CAAC,iBAAiB,OAAO,IAAI;QAE/C,sCAAsC;QACtC,MAAM,WAAW,6LAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;gBACP,SAAS;YACX;QACF;QAEA,6DAA6D;QAC7D,SAAS,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,EAAE;QAC7C,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,IAAI;QAEjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QAEnC,2CAA2C;QAC3C,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;IAC5D;AACF;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
;
async function middleware(request) {
    const { pathname } = request.nextUrl;
    // Skip middleware for static files, error pages, and API routes that don't need tenant context
    if (pathname.startsWith('/_next/') || pathname.startsWith('/api/health') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.startsWith('/tenant-not-found') || pathname.startsWith('/tenant-inactive') || pathname.startsWith('/error') || pathname.match(/\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    try {
        // Simple tenant resolution for Edge Runtime
        const host = request.headers.get('host') || '';
        const parts = host.split('.');
        let tenantSubdomain = 'default';
        let tenantName = 'GymD Platform';
        // For localhost development
        if (host.includes('localhost')) {
            const subdomain = parts[0].split(':')[0]; // Remove port from subdomain
            if (subdomain !== 'localhost') {
                tenantSubdomain = subdomain;
                tenantName = `${subdomain} Gym`;
            }
        } else if (parts.length >= 3) {
            // For production domains
            tenantSubdomain = parts[0];
            tenantName = `${parts[0]} Gym`;
        }
        // Clone the request headers and add tenant information
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set('x-tenant-subdomain', tenantSubdomain);
        requestHeaders.set('x-tenant-name', tenantName);
        // Create response with tenant headers
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next({
            request: {
                headers: requestHeaders
            }
        });
        // Add tenant info to response headers for client-side access
        response.headers.set('x-tenant-subdomain', tenantSubdomain);
        response.headers.set('x-tenant-name', tenantName);
        return response;
    } catch (error) {
        console.error('Middleware error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * But include API routes that need tenant context
     */ '/((?!_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map
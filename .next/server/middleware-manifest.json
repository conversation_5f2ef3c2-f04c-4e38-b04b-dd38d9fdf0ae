{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "CvsaYIkaUrVsjirfWh7lWRJGfd2HDvZk7heI6BSSAak=", "__NEXT_PREVIEW_MODE_ID": "b4f7da048c654e03f66111a83420a770", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9101e6a5712a000a727e7dfde803d2ae1ba5775d86b2b47afe2f9d878e33800e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5606e42121f4fd5159ba40668b486b28d6e4b8972323cd903f7abe0caa4c3cf0"}}}, "sortedMiddleware": ["/"], "functions": {}}
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nasync function middleware(request) {\n    const { pathname } = request.nextUrl;\n    // Skip middleware for static files, error pages, and API routes that don't need tenant context\n    if (pathname.startsWith('/_next/') || pathname.startsWith('/api/health') || pathname.startsWith('/favicon.ico') || pathname.startsWith('/public/') || pathname.startsWith('/tenant-not-found') || pathname.startsWith('/tenant-inactive') || pathname.startsWith('/error') || pathname.match(/\\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/)) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n    try {\n        // Simple tenant resolution for Edge Runtime\n        const host = request.headers.get('host') || '';\n        const parts = host.split('.');\n        let tenantSubdomain = 'default';\n        let tenantName = 'GymD Platform';\n        // For localhost development\n        if (host.includes('localhost')) {\n            const subdomain = parts[0].split(':')[0]; // Remove port from subdomain\n            if (subdomain !== 'localhost') {\n                tenantSubdomain = subdomain;\n                tenantName = `${subdomain} Gym`;\n            }\n        } else if (parts.length >= 3) {\n            // For production domains\n            tenantSubdomain = parts[0];\n            tenantName = `${parts[0]} Gym`;\n        }\n        // Clone the request headers and add tenant information\n        const requestHeaders = new Headers(request.headers);\n        requestHeaders.set('x-tenant-subdomain', tenantSubdomain);\n        requestHeaders.set('x-tenant-name', tenantName);\n        // Create response with tenant headers\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next({\n            request: {\n                headers: requestHeaders\n            }\n        });\n        // Add tenant info to response headers for client-side access\n        response.headers.set('x-tenant-subdomain', tenantSubdomain);\n        response.headers.set('x-tenant-name', tenantName);\n        return response;\n    } catch (error) {\n        console.error('Middleware error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n    }\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * But include API routes that need tenant context\n     */ '/((?!_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});
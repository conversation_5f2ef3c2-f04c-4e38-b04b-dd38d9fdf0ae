"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connectToDatabase: () => (/* binding */ connectToDatabase),\n/* harmony export */   disconnectFromDatabase: () => (/* binding */ disconnectFromDatabase)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"(middleware)/./node_modules/mongoose/dist/browser.umd.js\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nlet cached = __webpack_require__.g.mongooseCache || {\n    conn: null,\n    promise: null\n};\nif (!__webpack_require__.g.mongooseCache) {\n    __webpack_require__.g.mongooseCache = cached;\n}\nasync function connectToDatabase() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        const mongoUri = process.env.MONGODB_URI;\n        if (!mongoUri) {\n            throw new Error('MONGODB_URI environment variable is not defined');\n        }\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(mongoUri, opts);\n    }\n    try {\n        cached.conn = await cached.promise;\n        console.log('✅ Connected to MongoDB');\n        return cached.conn;\n    } catch (error) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', error);\n        throw error;\n    }\n}\nasync function disconnectFromDatabase() {\n    if (cached.conn) {\n        await cached.conn.disconnect();\n        cached.conn = null;\n        cached.promise = null;\n        console.log('🔌 Disconnected from MongoDB');\n    }\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n});\n// Graceful shutdown (only in Node.js runtime, not Edge Runtime)\nif (typeof process !== 'undefined' && process.on) {\n    process.on('SIGINT', async ()=>{\n        await disconnectFromDatabase();\n        process.exit(0);\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/lib/database/connection.ts\n");

/***/ })

});
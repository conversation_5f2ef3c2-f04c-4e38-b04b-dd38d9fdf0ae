/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_jerin_Documents_Projects_gymd_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./src/app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/Projects/gymd/src/app/api/auth/[...nextauth]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_jerin_Documents_Projects_gymd_src_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/[...nextauth]/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/auth/[...nextauth]/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth/config */ \"(rsc)/./src/lib/auth/config.ts\");\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBaUM7QUFDZTtBQUVoRCxNQUFNRSxVQUFVRixnREFBUUEsQ0FBQ0MseURBQVdBO0FBRU8iLCJzb3VyY2VzIjpbIi9Vc2Vycy9qZXJpbi9Eb2N1bWVudHMvUHJvamVjdHMvZ3ltZC9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE5leHRBdXRoIGZyb20gJ25leHQtYXV0aCc7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJ0AvbGliL2F1dGgvY29uZmlnJztcblxuY29uc3QgaGFuZGxlciA9IE5leHRBdXRoKGF1dGhPcHRpb25zKTtcblxuZXhwb3J0IHsgaGFuZGxlciBhcyBHRVQsIGhhbmRsZXIgYXMgUE9TVCB9O1xuIl0sIm5hbWVzIjpbIk5leHRBdXRoIiwiYXV0aE9wdGlvbnMiLCJoYW5kbGVyIiwiR0VUIiwiUE9TVCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/config.ts":
/*!********************************!*\
  !*** ./src/lib/auth/config.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/mongodb-adapter */ \"(rsc)/./node_modules/@auth/mongodb-adapter/index.js\");\n/* harmony import */ var _lib_database_connection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _models_User__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/models/User */ \"(rsc)/./src/models/User.ts\");\n/* harmony import */ var _models_Gym__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/models/Gym */ \"(rsc)/./src/models/Gym.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n\n\n// Login schema validation\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().email('Invalid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(1, 'Password is required'),\n    gymSubdomain: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().optional()\n});\nconst authOptions = {\n    adapter: (0,_auth_mongodb_adapter__WEBPACK_IMPORTED_MODULE_1__.MongoDBAdapter)((0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_2__.connectToDatabase)().then(({ db })=>db)),\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                },\n                gymSubdomain: {\n                    label: 'Gym Subdomain',\n                    type: 'text'\n                }\n            },\n            async authorize (credentials) {\n                try {\n                    if (!credentials?.email || !credentials?.password) {\n                        throw new Error('Email and password are required');\n                    }\n                    // Validate input\n                    const validatedCredentials = loginSchema.parse(credentials);\n                    // Connect to database\n                    await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_2__.connectToDatabase)();\n                    // Find user by email\n                    const user = await _models_User__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                        email: validatedCredentials.email.toLowerCase()\n                    }).select('+password');\n                    if (!user) {\n                        throw new Error('Invalid email or password');\n                    }\n                    // Check if user is locked\n                    if (user.isLocked) {\n                        throw new Error('Account is temporarily locked due to too many failed login attempts');\n                    }\n                    // Check if user status allows login\n                    if (user.status === _models_User__WEBPACK_IMPORTED_MODULE_3__.UserStatus.SUSPENDED) {\n                        throw new Error('Account is suspended');\n                    }\n                    if (user.status === _models_User__WEBPACK_IMPORTED_MODULE_3__.UserStatus.INACTIVE) {\n                        throw new Error('Account is inactive');\n                    }\n                    // Verify password\n                    const isPasswordValid = await user.comparePassword(validatedCredentials.password);\n                    if (!isPasswordValid) {\n                        // Increment login attempts\n                        await user.incrementLoginAttempts();\n                        throw new Error('Invalid email or password');\n                    }\n                    // Reset login attempts on successful login\n                    await user.resetLoginAttempts();\n                    // Handle gym-specific login\n                    let selectedGymId = user.gymId;\n                    if (validatedCredentials.gymSubdomain) {\n                        // Verify user has access to the specified gym\n                        const gym = await _models_Gym__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findOne({\n                            subdomain: validatedCredentials.gymSubdomain\n                        });\n                        if (!gym) {\n                            throw new Error('Gym not found');\n                        }\n                        // Check if user has access to this gym\n                        const hasAccess = user.role === _models_User__WEBPACK_IMPORTED_MODULE_3__.UserRole.SUPER_ADMIN || user.gymId === gym._id.toString() || user.gymIds && user.gymIds.includes(gym._id.toString());\n                        if (!hasAccess) {\n                            throw new Error('You do not have access to this gym');\n                        }\n                        selectedGymId = gym._id.toString();\n                    }\n                    // Return user object for NextAuth\n                    return {\n                        id: user._id.toString(),\n                        email: user.email,\n                        firstName: user.firstName,\n                        lastName: user.lastName,\n                        role: user.role,\n                        status: user.status,\n                        gymId: selectedGymId,\n                        gymIds: user.gymIds,\n                        avatar: user.avatar\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    throw new Error(error instanceof Error ? error.message : 'Authentication failed');\n                }\n            }\n        })\n    ],\n    session: {\n        strategy: 'jwt',\n        maxAge: 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 24 * 60 * 60\n    },\n    callbacks: {\n        async jwt ({ token, user, account }) {\n            // Initial sign in\n            if (account && user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.status = user.status;\n                token.gymId = user.gymId;\n                token.gymIds = user.gymIds;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.status = token.status;\n                session.user.gymId = token.gymId;\n                session.user.gymIds = token.gymIds;\n                session.user.fullName = `${session.user.firstName} ${session.user.lastName}`;\n                // Add gym information to session if user has a selected gym\n                if (token.gymId) {\n                    try {\n                        await (0,_lib_database_connection__WEBPACK_IMPORTED_MODULE_2__.connectToDatabase)();\n                        const gym = await _models_Gym__WEBPACK_IMPORTED_MODULE_4__[\"default\"].findById(token.gymId).select('name subdomain domain');\n                        if (gym) {\n                            session.gym = {\n                                id: gym._id.toString(),\n                                name: gym.name,\n                                subdomain: gym.subdomain,\n                                domain: gym.domain\n                            };\n                        }\n                    } catch (error) {\n                        console.error('Error fetching gym for session:', error);\n                    }\n                }\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Allows relative callback URLs\n            if (url.startsWith('/')) return `${baseUrl}${url}`;\n            // Allows callback URLs on the same origin\n            if (new URL(url).origin === baseUrl) return url;\n            return baseUrl;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin',\n        signUp: '/auth/signup',\n        error: '/auth/error',\n        verifyRequest: '/auth/verify-request',\n        newUser: '/auth/welcome'\n    },\n    events: {\n        async signIn ({ user, account, profile, isNewUser }) {\n            console.log('User signed in:', {\n                userId: user.id,\n                email: user.email,\n                role: user.role,\n                gymId: user.gymId\n            });\n        },\n        async signOut ({ session, token }) {\n            console.log('User signed out:', {\n                userId: token?.id || session?.user?.id\n            });\n        }\n    },\n    debug: \"development\" === 'development'\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authOptions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   connectToDatabase: () => (/* binding */ connectToDatabase),\n/* harmony export */   disconnectFromDatabase: () => (/* binding */ disconnectFromDatabase)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nlet cached = global.mongooseCache || {\n    conn: null,\n    promise: null\n};\nif (!global.mongooseCache) {\n    global.mongooseCache = cached;\n}\nasync function connectToDatabase() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false,\n            maxPoolSize: 10,\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000,\n            family: 4\n        };\n        const mongoUri = process.env.MONGODB_URI;\n        if (!mongoUri) {\n            throw new Error('MONGODB_URI environment variable is not defined');\n        }\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(mongoUri, opts);\n    }\n    try {\n        cached.conn = await cached.promise;\n        console.log('✅ Connected to MongoDB');\n        return cached.conn;\n    } catch (error) {\n        cached.promise = null;\n        console.error('❌ MongoDB connection error:', error);\n        throw error;\n    }\n}\nasync function disconnectFromDatabase() {\n    if (cached.conn) {\n        await cached.conn.disconnect();\n        cached.conn = null;\n        cached.promise = null;\n        console.log('🔌 Disconnected from MongoDB');\n    }\n}\n// Connection event handlers\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('connected', ()=>{\n    console.log('🔗 Mongoose connected to MongoDB');\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('error', (err)=>{\n    console.error('❌ Mongoose connection error:', err);\n});\nmongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on('disconnected', ()=>{\n    console.log('🔌 Mongoose disconnected from MongoDB');\n}); // Graceful shutdown (only in Node.js runtime, not Edge Runtime)\n // Note: Removed process handlers to avoid Edge Runtime compatibility issues\n // The connection will be handled by MongoDB driver's built-in connection pooling\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Gym.ts":
/*!***************************!*\
  !*** ./src/models/Gym.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst GymSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        trim: true,\n        maxlength: 100\n    },\n    subdomain: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true,\n        match: /^[a-z0-9-]+$/,\n        maxlength: 50\n    },\n    domain: {\n        type: String,\n        trim: true\n    },\n    logo: {\n        type: String,\n        trim: true\n    },\n    description: {\n        type: String,\n        trim: true,\n        maxlength: 500\n    },\n    address: {\n        street: {\n            type: String,\n            required: true,\n            trim: true\n        },\n        city: {\n            type: String,\n            required: true,\n            trim: true\n        },\n        state: {\n            type: String,\n            required: true,\n            trim: true\n        },\n        zipCode: {\n            type: String,\n            required: true,\n            trim: true\n        },\n        country: {\n            type: String,\n            required: true,\n            trim: true,\n            default: 'US'\n        }\n    },\n    contact: {\n        phone: {\n            type: String,\n            required: true,\n            trim: true\n        },\n        email: {\n            type: String,\n            required: true,\n            trim: true,\n            lowercase: true\n        },\n        website: {\n            type: String,\n            trim: true\n        }\n    },\n    settings: {\n        timezone: {\n            type: String,\n            default: 'America/New_York'\n        },\n        currency: {\n            type: String,\n            default: 'USD'\n        },\n        language: {\n            type: String,\n            default: 'en'\n        },\n        dateFormat: {\n            type: String,\n            default: 'MM/DD/YYYY'\n        },\n        businessHours: {\n            type: Map,\n            of: {\n                open: {\n                    type: String,\n                    default: '06:00'\n                },\n                close: {\n                    type: String,\n                    default: '22:00'\n                },\n                isOpen: {\n                    type: Boolean,\n                    default: true\n                }\n            },\n            default: {\n                monday: {\n                    open: '06:00',\n                    close: '22:00',\n                    isOpen: true\n                },\n                tuesday: {\n                    open: '06:00',\n                    close: '22:00',\n                    isOpen: true\n                },\n                wednesday: {\n                    open: '06:00',\n                    close: '22:00',\n                    isOpen: true\n                },\n                thursday: {\n                    open: '06:00',\n                    close: '22:00',\n                    isOpen: true\n                },\n                friday: {\n                    open: '06:00',\n                    close: '22:00',\n                    isOpen: true\n                },\n                saturday: {\n                    open: '08:00',\n                    close: '20:00',\n                    isOpen: true\n                },\n                sunday: {\n                    open: '08:00',\n                    close: '20:00',\n                    isOpen: true\n                }\n            }\n        }\n    },\n    subscription: {\n        plan: {\n            type: String,\n            enum: [\n                'free',\n                'basic',\n                'premium',\n                'enterprise'\n            ],\n            default: 'free'\n        },\n        status: {\n            type: String,\n            enum: [\n                'active',\n                'inactive',\n                'suspended',\n                'cancelled'\n            ],\n            default: 'active'\n        },\n        startDate: {\n            type: Date,\n            default: Date.now\n        },\n        endDate: Date,\n        maxMembers: {\n            type: Number,\n            default: 50\n        },\n        maxTrainers: {\n            type: Number,\n            default: 5\n        }\n    },\n    whatsapp: {\n        enabled: {\n            type: Boolean,\n            default: false\n        },\n        accountSid: String,\n        authToken: String,\n        phoneNumber: String\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes\nGymSchema.index({\n    subdomain: 1\n});\nGymSchema.index({\n    domain: 1\n});\nGymSchema.index({\n    isActive: 1\n});\nGymSchema.index({\n    'subscription.status': 1\n});\n// Virtual for full address\nGymSchema.virtual('fullAddress').get(function() {\n    return `${this.address.street}, ${this.address.city}, ${this.address.state} ${this.address.zipCode}, ${this.address.country}`;\n});\n// Pre-save middleware\nGymSchema.pre('save', function(next) {\n    if (this.isModified('subdomain') || this.isNew) {\n        this.domain = `${this.subdomain}.${process.env.APP_DOMAIN || 'localhost:3000'}`;\n    }\n    next();\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Gym || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Gym', GymSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Gym.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/User.ts":
/*!****************************!*\
  !*** ./src/models/User.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   UserStatus: () => (/* binding */ UserStatus),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n\n\n// User roles enum\nvar UserRole = /*#__PURE__*/ function(UserRole) {\n    UserRole[\"SUPER_ADMIN\"] = \"super_admin\";\n    UserRole[\"GYM_OWNER\"] = \"gym_owner\";\n    UserRole[\"GYM_ADMIN\"] = \"gym_admin\";\n    UserRole[\"GYM_STAFF\"] = \"gym_staff\";\n    UserRole[\"MEMBER\"] = \"member\";\n    return UserRole;\n}({});\n// User status enum\nvar UserStatus = /*#__PURE__*/ function(UserStatus) {\n    UserStatus[\"ACTIVE\"] = \"active\";\n    UserStatus[\"INACTIVE\"] = \"inactive\";\n    UserStatus[\"SUSPENDED\"] = \"suspended\";\n    UserStatus[\"PENDING\"] = \"pending\";\n    return UserStatus;\n}({});\n// User schema\nconst UserSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    // Basic Information\n    email: {\n        type: String,\n        required: [\n            true,\n            'Email is required'\n        ],\n        unique: true,\n        lowercase: true,\n        trim: true,\n        match: [\n            /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\n            'Please enter a valid email'\n        ]\n    },\n    password: {\n        type: String,\n        required: [\n            true,\n            'Password is required'\n        ],\n        minlength: [\n            8,\n            'Password must be at least 8 characters long'\n        ],\n        select: false // Don't include password in queries by default\n    },\n    firstName: {\n        type: String,\n        required: [\n            true,\n            'First name is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'First name cannot exceed 50 characters'\n        ]\n    },\n    lastName: {\n        type: String,\n        required: [\n            true,\n            'Last name is required'\n        ],\n        trim: true,\n        maxlength: [\n            50,\n            'Last name cannot exceed 50 characters'\n        ]\n    },\n    phone: {\n        type: String,\n        trim: true,\n        match: [\n            /^\\+?[\\d\\s\\-\\(\\)]+$/,\n            'Please enter a valid phone number'\n        ]\n    },\n    avatar: {\n        type: String,\n        trim: true\n    },\n    // Role and Permissions\n    role: {\n        type: String,\n        enum: Object.values(UserRole),\n        required: [\n            true,\n            'User role is required'\n        ],\n        default: \"member\"\n    },\n    status: {\n        type: String,\n        enum: Object.values(UserStatus),\n        default: \"pending\"\n    },\n    // Multi-tenant Association\n    gymId: {\n        type: String,\n        ref: 'Gym',\n        required: function() {\n            // gymId is required for all roles except super_admin\n            return this.role !== \"super_admin\";\n        },\n        index: true\n    },\n    gymIds: [\n        {\n            type: String,\n            ref: 'Gym'\n        }\n    ],\n    // Authentication\n    emailVerified: {\n        type: Date\n    },\n    emailVerificationToken: {\n        type: String,\n        select: false\n    },\n    passwordResetToken: {\n        type: String,\n        select: false\n    },\n    passwordResetExpires: {\n        type: Date,\n        select: false\n    },\n    lastLogin: {\n        type: Date\n    },\n    loginAttempts: {\n        type: Number,\n        default: 0\n    },\n    lockUntil: {\n        type: Date\n    },\n    // Profile Information\n    dateOfBirth: {\n        type: Date\n    },\n    gender: {\n        type: String,\n        enum: [\n            'male',\n            'female',\n            'other'\n        ]\n    },\n    address: {\n        street: {\n            type: String,\n            trim: true\n        },\n        city: {\n            type: String,\n            trim: true\n        },\n        state: {\n            type: String,\n            trim: true\n        },\n        zipCode: {\n            type: String,\n            trim: true\n        },\n        country: {\n            type: String,\n            trim: true,\n            default: 'US'\n        }\n    },\n    // Preferences\n    preferences: {\n        notifications: {\n            email: {\n                type: Boolean,\n                default: true\n            },\n            sms: {\n                type: Boolean,\n                default: false\n            },\n            whatsapp: {\n                type: Boolean,\n                default: false\n            }\n        },\n        language: {\n            type: String,\n            default: 'en'\n        },\n        timezone: {\n            type: String,\n            default: 'UTC'\n        }\n    }\n}, {\n    timestamps: true,\n    toJSON: {\n        virtuals: true\n    },\n    toObject: {\n        virtuals: true\n    }\n});\n// Indexes for performance\nUserSchema.index({\n    email: 1\n});\nUserSchema.index({\n    gymId: 1,\n    role: 1\n});\nUserSchema.index({\n    status: 1\n});\nUserSchema.index({\n    createdAt: -1\n});\n// Virtual properties\nUserSchema.virtual('fullName').get(function() {\n    return `${this.firstName} ${this.lastName}`.trim();\n});\nUserSchema.virtual('isLocked').get(function() {\n    return !!(this.lockUntil && this.lockUntil > new Date());\n});\n// Pre-save middleware\nUserSchema.pre('save', async function(next) {\n    // Only hash password if it's modified\n    if (!this.isModified('password')) return next();\n    try {\n        // Hash password with cost of 12\n        const salt = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].genSalt(12);\n        this.password = await bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].hash(this.password, salt);\n        next();\n    } catch (error) {\n        next(error);\n    }\n});\n// Pre-save middleware for gym association\nUserSchema.pre('save', function(next) {\n    // For super_admin, ensure gymIds array exists\n    if (this.role === \"super_admin\" && !this.gymIds) {\n        this.gymIds = [];\n    }\n    // For gym_owner, add gymId to gymIds array if not already present\n    if (this.role === \"gym_owner\" && this.gymId) {\n        if (!this.gymIds) this.gymIds = [];\n        if (!this.gymIds.includes(this.gymId)) {\n            this.gymIds.push(this.gymId);\n        }\n    }\n    next();\n});\n// Instance methods\nUserSchema.methods.comparePassword = async function(candidatePassword) {\n    if (!this.password) return false;\n    return bcryptjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"].compare(candidatePassword, this.password);\n};\nUserSchema.methods.generatePasswordResetToken = function() {\n    const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.passwordResetToken = resetToken;\n    this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes\n    return resetToken;\n};\nUserSchema.methods.generateEmailVerificationToken = function() {\n    const verificationToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    this.emailVerificationToken = verificationToken;\n    return verificationToken;\n};\nUserSchema.methods.incrementLoginAttempts = async function() {\n    // If we have a previous lock that has expired, restart at 1\n    if (this.lockUntil && this.lockUntil < new Date()) {\n        return this.updateOne({\n            $unset: {\n                lockUntil: 1\n            },\n            $set: {\n                loginAttempts: 1\n            }\n        });\n    }\n    const updates = {\n        $inc: {\n            loginAttempts: 1\n        }\n    };\n    // Lock account after 5 failed attempts for 2 hours\n    if (this.loginAttempts + 1 >= 5 && !this.isLocked) {\n        updates.$set = {\n            lockUntil: new Date(Date.now() + 2 * 60 * 60 * 1000)\n        };\n    }\n    return this.updateOne(updates);\n};\nUserSchema.methods.resetLoginAttempts = async function() {\n    return this.updateOne({\n        $unset: {\n            loginAttempts: 1,\n            lockUntil: 1\n        },\n        $set: {\n            lastLogin: new Date()\n        }\n    });\n};\n// Export the model\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).User || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('User', UserSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/User.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongodb");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/zod","vendor-chunks/oauth","vendor-chunks/lru-cache","vendor-chunks/@panva","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/bcryptjs","vendor-chunks/@auth","vendor-chunks/preact","vendor-chunks/object-hash","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fjerin%2FDocuments%2FProjects%2Fgymd&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
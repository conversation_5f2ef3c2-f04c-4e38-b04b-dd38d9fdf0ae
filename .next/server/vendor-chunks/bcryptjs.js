"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/index.js":
/*!****************************************!*\
  !*** ./node_modules/bcryptjs/index.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compare: () => (/* binding */ compare),\n/* harmony export */   compareSync: () => (/* binding */ compareSync),\n/* harmony export */   decodeBase64: () => (/* binding */ decodeBase64),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   encodeBase64: () => (/* binding */ encodeBase64),\n/* harmony export */   genSalt: () => (/* binding */ genSalt),\n/* harmony export */   genSaltSync: () => (/* binding */ genSaltSync),\n/* harmony export */   getRounds: () => (/* binding */ getRounds),\n/* harmony export */   getSalt: () => (/* binding */ getSalt),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   hashSync: () => (/* binding */ hashSync),\n/* harmony export */   setRandomFallback: () => (/* binding */ setRandomFallback),\n/* harmony export */   truncates: () => (/* binding */ truncates)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/*\n Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\n Copyright (c) 2012 Shane Girish <<EMAIL>>\n Copyright (c) 2025 Daniel Wirtz <<EMAIL>>\n\n Redistribution and use in source and binary forms, with or without\n modification, are permitted provided that the following conditions\n are met:\n 1. Redistributions of source code must retain the above copyright\n notice, this list of conditions and the following disclaimer.\n 2. Redistributions in binary form must reproduce the above copyright\n notice, this list of conditions and the following disclaimer in the\n documentation and/or other materials provided with the distribution.\n 3. The name of the author may not be used to endorse or promote products\n derived from this software without specific prior written permission.\n\n THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n// The Node.js crypto module is used as a fallback for the Web Crypto API. When\n// building for the browser, inclusion of the crypto module should be disabled,\n// which the package hints at in its package.json for bundlers that support it.\n\n\n/**\n * The random implementation to use as a fallback.\n * @type {?function(number):!Array.<number>}\n * @inner\n */\nvar randomFallback = null;\n\n/**\n * Generates cryptographically secure random bytes.\n * @function\n * @param {number} len Bytes length\n * @returns {!Array.<number>} Random bytes\n * @throws {Error} If no random implementation is available\n * @inner\n */\nfunction randomBytes(len) {\n  // Web Crypto API. Globally available in the browser and in Node.js >=23.\n  try {\n    return crypto.getRandomValues(new Uint8Array(len));\n  } catch {}\n  // Node.js crypto module for non-browser environments.\n  try {\n    return crypto__WEBPACK_IMPORTED_MODULE_0__.randomBytes(len);\n  } catch {}\n  // Custom fallback specified with `setRandomFallback`.\n  if (!randomFallback) {\n    throw Error(\n      \"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\",\n    );\n  }\n  return randomFallback(len);\n}\n\n/**\n * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n *  is seeded properly!\n * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n * @see http://nodejs.org/api/crypto.html\n * @see http://www.w3.org/TR/WebCryptoAPI/\n */\nfunction setRandomFallback(random) {\n  randomFallback = random;\n}\n\n/**\n * Synchronously generates a salt.\n * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {number=} seed_length Not supported.\n * @returns {string} Resulting salt\n * @throws {Error} If a random fallback is required but not set\n */\nfunction genSaltSync(rounds, seed_length) {\n  rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof rounds !== \"number\")\n    throw Error(\n      \"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length,\n    );\n  if (rounds < 4) rounds = 4;\n  else if (rounds > 31) rounds = 31;\n  var salt = [];\n  salt.push(\"$2b$\");\n  if (rounds < 10) salt.push(\"0\");\n  salt.push(rounds.toString());\n  salt.push(\"$\");\n  salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n  return salt.join(\"\");\n}\n\n/**\n * Asynchronously generates a salt.\n * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n * @param {(number|function(Error, string=))=} seed_length Not supported.\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction genSalt(rounds, seed_length, callback) {\n  if (typeof seed_length === \"function\")\n    (callback = seed_length), (seed_length = undefined); // Not supported.\n  if (typeof rounds === \"function\") (callback = rounds), (rounds = undefined);\n  if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n  else if (typeof rounds !== \"number\")\n    throw Error(\"illegal arguments: \" + typeof rounds);\n\n  function _async(callback) {\n    nextTick(function () {\n      // Pretty thin, but salting is fast enough\n      try {\n        callback(null, genSaltSync(rounds));\n      } catch (err) {\n        callback(err);\n      }\n    });\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Synchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n * @returns {string} Resulting hash\n */\nfunction hashSync(password, salt) {\n  if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n  if (typeof salt === \"number\") salt = genSaltSync(salt);\n  if (typeof password !== \"string\" || typeof salt !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n  return _hash(password, salt);\n}\n\n/**\n * Asynchronously generates a hash for the given password.\n * @param {string} password Password to hash\n * @param {number|string} salt Salt length to generate or salt to use\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction hash(password, salt, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password === \"string\" && typeof salt === \"number\")\n      genSalt(salt, function (err, salt) {\n        _hash(password, salt, callback, progressCallback);\n      });\n    else if (typeof password === \"string\" && typeof salt === \"string\")\n      _hash(password, salt, callback, progressCallback);\n    else\n      nextTick(\n        callback.bind(\n          this,\n          Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt),\n        ),\n      );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Compares two strings of the same length in constant time.\n * @param {string} known Must be of the correct length\n * @param {string} unknown Must be the same length as `known`\n * @returns {boolean}\n * @inner\n */\nfunction safeStringCompare(known, unknown) {\n  var diff = known.length ^ unknown.length;\n  for (var i = 0; i < known.length; ++i) {\n    diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n  }\n  return diff === 0;\n}\n\n/**\n * Synchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hash Hash to test against\n * @returns {boolean} true if matching, otherwise false\n * @throws {Error} If an argument is illegal\n */\nfunction compareSync(password, hash) {\n  if (typeof password !== \"string\" || typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n  if (hash.length !== 60) return false;\n  return safeStringCompare(\n    hashSync(password, hash.substring(0, hash.length - 31)),\n    hash,\n  );\n}\n\n/**\n * Asynchronously tests a password against a hash.\n * @param {string} password Password to compare\n * @param {string} hashValue Hash to test against\n * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n * @returns {!Promise} If `callback` has been omitted\n * @throws {Error} If `callback` is present but not a function\n */\nfunction compare(password, hashValue, callback, progressCallback) {\n  function _async(callback) {\n    if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n      nextTick(\n        callback.bind(\n          this,\n          Error(\n            \"Illegal arguments: \" + typeof password + \", \" + typeof hashValue,\n          ),\n        ),\n      );\n      return;\n    }\n    if (hashValue.length !== 60) {\n      nextTick(callback.bind(this, null, false));\n      return;\n    }\n    hash(\n      password,\n      hashValue.substring(0, 29),\n      function (err, comp) {\n        if (err) callback(err);\n        else callback(null, safeStringCompare(comp, hashValue));\n      },\n      progressCallback,\n    );\n  }\n\n  if (callback) {\n    if (typeof callback !== \"function\")\n      throw Error(\"Illegal callback: \" + typeof callback);\n    _async(callback);\n  } else\n    return new Promise(function (resolve, reject) {\n      _async(function (err, res) {\n        if (err) {\n          reject(err);\n          return;\n        }\n        resolve(res);\n      });\n    });\n}\n\n/**\n * Gets the number of rounds used to encrypt the specified hash.\n * @param {string} hash Hash to extract the used number of rounds from\n * @returns {number} Number of rounds used\n * @throws {Error} If `hash` is not a string\n */\nfunction getRounds(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  return parseInt(hash.split(\"$\")[2], 10);\n}\n\n/**\n * Gets the salt portion from a hash. Does not validate the hash.\n * @param {string} hash Hash to extract the salt from\n * @returns {string} Extracted salt part\n * @throws {Error} If `hash` is not a string or otherwise invalid\n */\nfunction getSalt(hash) {\n  if (typeof hash !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof hash);\n  if (hash.length !== 60)\n    throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n  return hash.substring(0, 29);\n}\n\n/**\n * Tests if a password will be truncated when hashed, that is its length is\n * greater than 72 bytes when converted to UTF-8.\n * @param {string} password The password to test\n * @returns {boolean} `true` if truncated, otherwise `false`\n */\nfunction truncates(password) {\n  if (typeof password !== \"string\")\n    throw Error(\"Illegal arguments: \" + typeof password);\n  return utf8Length(password) > 72;\n}\n\n/**\n * Continues with the callback on the next tick.\n * @function\n * @param {function(...[*])} callback Callback to execute\n * @inner\n */\nvar nextTick =\n  typeof process !== \"undefined\" &&\n  process &&\n  typeof process.nextTick === \"function\"\n    ? typeof setImmediate === \"function\"\n      ? setImmediate\n      : process.nextTick\n    : setTimeout;\n\n/** Calculates the byte length of a string encoded as UTF8. */\nfunction utf8Length(string) {\n  var len = 0,\n    c = 0;\n  for (var i = 0; i < string.length; ++i) {\n    c = string.charCodeAt(i);\n    if (c < 128) len += 1;\n    else if (c < 2048) len += 2;\n    else if (\n      (c & 0xfc00) === 0xd800 &&\n      (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00\n    ) {\n      ++i;\n      len += 4;\n    } else len += 3;\n  }\n  return len;\n}\n\n/** Converts a string to an array of UTF8 bytes. */\nfunction utf8Array(string) {\n  var offset = 0,\n    c1,\n    c2;\n  var buffer = new Array(utf8Length(string));\n  for (var i = 0, k = string.length; i < k; ++i) {\n    c1 = string.charCodeAt(i);\n    if (c1 < 128) {\n      buffer[offset++] = c1;\n    } else if (c1 < 2048) {\n      buffer[offset++] = (c1 >> 6) | 192;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else if (\n      (c1 & 0xfc00) === 0xd800 &&\n      ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00\n    ) {\n      c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n      ++i;\n      buffer[offset++] = (c1 >> 18) | 240;\n      buffer[offset++] = ((c1 >> 12) & 63) | 128;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    } else {\n      buffer[offset++] = (c1 >> 12) | 224;\n      buffer[offset++] = ((c1 >> 6) & 63) | 128;\n      buffer[offset++] = (c1 & 63) | 128;\n    }\n  }\n  return buffer;\n}\n\n// A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n\n/**\n * bcrypt's own non-standard base64 dictionary.\n * @type {!Array.<string>}\n * @const\n * @inner\n **/\nvar BASE64_CODE =\n  \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n\n/**\n * @type {!Array.<number>}\n * @const\n * @inner\n **/\nvar BASE64_INDEX = [\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n  -1, -1, -1, -1, -1, -1, -1, -1, 0, 1, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63,\n  -1, -1, -1, -1, -1, -1, -1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15,\n  16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, -1, -1, -1, -1, -1, -1, 28,\n  29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47,\n  48, 49, 50, 51, 52, 53, -1, -1, -1, -1, -1,\n];\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input.\n * @param {!Array.<number>} b Byte array\n * @param {number} len Maximum input length\n * @returns {string}\n * @inner\n */\nfunction base64_encode(b, len) {\n  var off = 0,\n    rs = [],\n    c1,\n    c2;\n  if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n  while (off < len) {\n    c1 = b[off++] & 0xff;\n    rs.push(BASE64_CODE[(c1 >> 2) & 0x3f]);\n    c1 = (c1 & 0x03) << 4;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 4) & 0x0f;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    c1 = (c2 & 0x0f) << 2;\n    if (off >= len) {\n      rs.push(BASE64_CODE[c1 & 0x3f]);\n      break;\n    }\n    c2 = b[off++] & 0xff;\n    c1 |= (c2 >> 6) & 0x03;\n    rs.push(BASE64_CODE[c1 & 0x3f]);\n    rs.push(BASE64_CODE[c2 & 0x3f]);\n  }\n  return rs.join(\"\");\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output.\n * @param {string} s String to decode\n * @param {number} len Maximum output length\n * @returns {!Array.<number>}\n * @inner\n */\nfunction base64_decode(s, len) {\n  var off = 0,\n    slen = s.length,\n    olen = 0,\n    rs = [],\n    c1,\n    c2,\n    c3,\n    c4,\n    o,\n    code;\n  if (len <= 0) throw Error(\"Illegal len: \" + len);\n  while (off < slen - 1 && olen < len) {\n    code = s.charCodeAt(off++);\n    c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    code = s.charCodeAt(off++);\n    c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c1 == -1 || c2 == -1) break;\n    o = (c1 << 2) >>> 0;\n    o |= (c2 & 0x30) >> 4;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    if (c3 == -1) break;\n    o = ((c2 & 0x0f) << 4) >>> 0;\n    o |= (c3 & 0x3c) >> 2;\n    rs.push(String.fromCharCode(o));\n    if (++olen >= len || off >= slen) break;\n    code = s.charCodeAt(off++);\n    c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n    o = ((c3 & 0x03) << 6) >>> 0;\n    o |= c4;\n    rs.push(String.fromCharCode(o));\n    ++olen;\n  }\n  var res = [];\n  for (off = 0; off < olen; off++) res.push(rs[off].charCodeAt(0));\n  return res;\n}\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BCRYPT_SALT_LEN = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar BLOWFISH_NUM_ROUNDS = 16;\n\n/**\n * @type {number}\n * @const\n * @inner\n */\nvar MAX_EXECUTION_TIME = 100;\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar P_ORIG = [\n  0x243f6a88, 0x85a308d3, 0x13198a2e, 0x03707344, 0xa4093822, 0x299f31d0,\n  0x082efa98, 0xec4e6c89, 0x452821e6, 0x38d01377, 0xbe5466cf, 0x34e90c6c,\n  0xc0ac29b7, 0xc97c50dd, 0x3f84d5b5, 0xb5470917, 0x9216d5d9, 0x8979fb1b,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar S_ORIG = [\n  0xd1310ba6, 0x98dfb5ac, 0x2ffd72db, 0xd01adfb7, 0xb8e1afed, 0x6a267e96,\n  0xba7c9045, 0xf12c7f99, 0x24a19947, 0xb3916cf7, 0x0801f2e2, 0x858efc16,\n  0x636920d8, 0x71574e69, 0xa458fea3, 0xf4933d7e, 0x0d95748f, 0x728eb658,\n  0x718bcd58, 0x82154aee, 0x7b54a41d, 0xc25a59b5, 0x9c30d539, 0x2af26013,\n  0xc5d1b023, 0x286085f0, 0xca417918, 0xb8db38ef, 0x8e79dcb0, 0x603a180e,\n  0x6c9e0e8b, 0xb01e8a3e, 0xd71577c1, 0xbd314b27, 0x78af2fda, 0x55605c60,\n  0xe65525f3, 0xaa55ab94, 0x57489862, 0x63e81440, 0x55ca396a, 0x2aab10b6,\n  0xb4cc5c34, 0x1141e8ce, 0xa15486af, 0x7c72e993, 0xb3ee1411, 0x636fbc2a,\n  0x2ba9c55d, 0x741831f6, 0xce5c3e16, 0x9b87931e, 0xafd6ba33, 0x6c24cf5c,\n  0x7a325381, 0x28958677, 0x3b8f4898, 0x6b4bb9af, 0xc4bfe81b, 0x66282193,\n  0x61d809cc, 0xfb21a991, 0x487cac60, 0x5dec8032, 0xef845d5d, 0xe98575b1,\n  0xdc262302, 0xeb651b88, 0x23893e81, 0xd396acc5, 0x0f6d6ff3, 0x83f44239,\n  0x2e0b4482, 0xa4842004, 0x69c8f04a, 0x9e1f9b5e, 0x21c66842, 0xf6e96c9a,\n  0x670c9c61, 0xabd388f0, 0x6a51a0d2, 0xd8542f68, 0x960fa728, 0xab5133a3,\n  0x6eef0b6c, 0x137a3be4, 0xba3bf050, 0x7efb2a98, 0xa1f1651d, 0x39af0176,\n  0x66ca593e, 0x82430e88, 0x8cee8619, 0x456f9fb4, 0x7d84a5c3, 0x3b8b5ebe,\n  0xe06f75d8, 0x85c12073, 0x401a449f, 0x56c16aa6, 0x4ed3aa62, 0x363f7706,\n  0x1bfedf72, 0x429b023d, 0x37d0d724, 0xd00a1248, 0xdb0fead3, 0x49f1c09b,\n  0x075372c9, 0x80991b7b, 0x25d479d8, 0xf6e8def7, 0xe3fe501a, 0xb6794c3b,\n  0x976ce0bd, 0x04c006ba, 0xc1a94fb6, 0x409f60c4, 0x5e5c9ec2, 0x196a2463,\n  0x68fb6faf, 0x3e6c53b5, 0x1339b2eb, 0x3b52ec6f, 0x6dfc511f, 0x9b30952c,\n  0xcc814544, 0xaf5ebd09, 0xbee3d004, 0xde334afd, 0x660f2807, 0x192e4bb3,\n  0xc0cba857, 0x45c8740f, 0xd20b5f39, 0xb9d3fbdb, 0x5579c0bd, 0x1a60320a,\n  0xd6a100c6, 0x402c7279, 0x679f25fe, 0xfb1fa3cc, 0x8ea5e9f8, 0xdb3222f8,\n  0x3c7516df, 0xfd616b15, 0x2f501ec8, 0xad0552ab, 0x323db5fa, 0xfd238760,\n  0x53317b48, 0x3e00df82, 0x9e5c57bb, 0xca6f8ca0, 0x1a87562e, 0xdf1769db,\n  0xd542a8f6, 0x287effc3, 0xac6732c6, 0x8c4f5573, 0x695b27b0, 0xbbca58c8,\n  0xe1ffa35d, 0xb8f011a0, 0x10fa3d98, 0xfd2183b8, 0x4afcb56c, 0x2dd1d35b,\n  0x9a53e479, 0xb6f84565, 0xd28e49bc, 0x4bfb9790, 0xe1ddf2da, 0xa4cb7e33,\n  0x62fb1341, 0xcee4c6e8, 0xef20cada, 0x36774c01, 0xd07e9efe, 0x2bf11fb4,\n  0x95dbda4d, 0xae909198, 0xeaad8e71, 0x6b93d5a0, 0xd08ed1d0, 0xafc725e0,\n  0x8e3c5b2f, 0x8e7594b7, 0x8ff6e2fb, 0xf2122b64, 0x8888b812, 0x900df01c,\n  0x4fad5ea0, 0x688fc31c, 0xd1cff191, 0xb3a8c1ad, 0x2f2f2218, 0xbe0e1777,\n  0xea752dfe, 0x8b021fa1, 0xe5a0cc0f, 0xb56f74e8, 0x18acf3d6, 0xce89e299,\n  0xb4a84fe0, 0xfd13e0b7, 0x7cc43b81, 0xd2ada8d9, 0x165fa266, 0x80957705,\n  0x93cc7314, 0x211a1477, 0xe6ad2065, 0x77b5fa86, 0xc75442f5, 0xfb9d35cf,\n  0xebcdaf0c, 0x7b3e89a0, 0xd6411bd3, 0xae1e7e49, 0x00250e2d, 0x2071b35e,\n  0x226800bb, 0x57b8e0af, 0x2464369b, 0xf009b91e, 0x5563911d, 0x59dfa6aa,\n  0x78c14389, 0xd95a537f, 0x207d5ba2, 0x02e5b9c5, 0x83260376, 0x6295cfa9,\n  0x11c81968, 0x4e734a41, 0xb3472dca, 0x7b14a94a, 0x1b510052, 0x9a532915,\n  0xd60f573f, 0xbc9bc6e4, 0x2b60a476, 0x81e67400, 0x08ba6fb5, 0x571be91f,\n  0xf296ec6b, 0x2a0dd915, 0xb6636521, 0xe7b9f9b6, 0xff34052e, 0xc5855664,\n  0x53b02d5d, 0xa99f8fa1, 0x08ba4799, 0x6e85076a, 0x4b7a70e9, 0xb5b32944,\n  0xdb75092e, 0xc4192623, 0xad6ea6b0, 0x49a7df7d, 0x9cee60b8, 0x8fedb266,\n  0xecaa8c71, 0x699a17ff, 0x5664526c, 0xc2b19ee1, 0x193602a5, 0x75094c29,\n  0xa0591340, 0xe4183a3e, 0x3f54989a, 0x5b429d65, 0x6b8fe4d6, 0x99f73fd6,\n  0xa1d29c07, 0xefe830f5, 0x4d2d38e6, 0xf0255dc1, 0x4cdd2086, 0x8470eb26,\n  0x6382e9c6, 0x021ecc5e, 0x09686b3f, 0x3ebaefc9, 0x3c971814, 0x6b6a70a1,\n  0x687f3584, 0x52a0e286, 0xb79c5305, 0xaa500737, 0x3e07841c, 0x7fdeae5c,\n  0x8e7d44ec, 0x5716f2b8, 0xb03ada37, 0xf0500c0d, 0xf01c1f04, 0x0200b3ff,\n  0xae0cf51a, 0x3cb574b2, 0x25837a58, 0xdc0921bd, 0xd19113f9, 0x7ca92ff6,\n  0x94324773, 0x22f54701, 0x3ae5e581, 0x37c2dadc, 0xc8b57634, 0x9af3dda7,\n  0xa9446146, 0x0fd0030e, 0xecc8c73e, 0xa4751e41, 0xe238cd99, 0x3bea0e2f,\n  0x3280bba1, 0x183eb331, 0x4e548b38, 0x4f6db908, 0x6f420d03, 0xf60a04bf,\n  0x2cb81290, 0x24977c79, 0x5679b072, 0xbcaf89af, 0xde9a771f, 0xd9930810,\n  0xb38bae12, 0xdccf3f2e, 0x5512721f, 0x2e6b7124, 0x501adde6, 0x9f84cd87,\n  0x7a584718, 0x7408da17, 0xbc9f9abc, 0xe94b7d8c, 0xec7aec3a, 0xdb851dfa,\n  0x63094366, 0xc464c3d2, 0xef1c1847, 0x3215d908, 0xdd433b37, 0x24c2ba16,\n  0x12a14d43, 0x2a65c451, 0x50940002, 0x133ae4dd, 0x71dff89e, 0x10314e55,\n  0x81ac77d6, 0x5f11199b, 0x043556f1, 0xd7a3c76b, 0x3c11183b, 0x5924a509,\n  0xf28fe6ed, 0x97f1fbfa, 0x9ebabf2c, 0x1e153c6e, 0x86e34570, 0xeae96fb1,\n  0x860e5e0a, 0x5a3e2ab3, 0x771fe71c, 0x4e3d06fa, 0x2965dcb9, 0x99e71d0f,\n  0x803e89d6, 0x5266c825, 0x2e4cc978, 0x9c10b36a, 0xc6150eba, 0x94e2ea78,\n  0xa5fc3c53, 0x1e0a2df4, 0xf2f74ea7, 0x361d2b3d, 0x1939260f, 0x19c27960,\n  0x5223a708, 0xf71312b6, 0xebadfe6e, 0xeac31f66, 0xe3bc4595, 0xa67bc883,\n  0xb17f37d1, 0x018cff28, 0xc332ddef, 0xbe6c5aa5, 0x65582185, 0x68ab9802,\n  0xeecea50f, 0xdb2f953b, 0x2aef7dad, 0x5b6e2f84, 0x1521b628, 0x29076170,\n  0xecdd4775, 0x619f1510, 0x13cca830, 0xeb61bd96, 0x0334fe1e, 0xaa0363cf,\n  0xb5735c90, 0x4c70a239, 0xd59e9e0b, 0xcbaade14, 0xeecc86bc, 0x60622ca7,\n  0x9cab5cab, 0xb2f3846e, 0x648b1eaf, 0x19bdf0ca, 0xa02369b9, 0x655abb50,\n  0x40685a32, 0x3c2ab4b3, 0x319ee9d5, 0xc021b8f7, 0x9b540b19, 0x875fa099,\n  0x95f7997e, 0x623d7da8, 0xf837889a, 0x97e32d77, 0x11ed935f, 0x16681281,\n  0x0e358829, 0xc7e61fd6, 0x96dedfa1, 0x7858ba99, 0x57f584a5, 0x1b227263,\n  0x9b83c3ff, 0x1ac24696, 0xcdb30aeb, 0x532e3054, 0x8fd948e4, 0x6dbc3128,\n  0x58ebf2ef, 0x34c6ffea, 0xfe28ed61, 0xee7c3c73, 0x5d4a14d9, 0xe864b7e3,\n  0x42105d14, 0x203e13e0, 0x45eee2b6, 0xa3aaabea, 0xdb6c4f15, 0xfacb4fd0,\n  0xc742f442, 0xef6abbb5, 0x654f3b1d, 0x41cd2105, 0xd81e799e, 0x86854dc7,\n  0xe44b476a, 0x3d816250, 0xcf62a1f2, 0x5b8d2646, 0xfc8883a0, 0xc1c7b6a3,\n  0x7f1524c3, 0x69cb7492, 0x47848a0b, 0x5692b285, 0x095bbf00, 0xad19489d,\n  0x1462b174, 0x23820e00, 0x58428d2a, 0x0c55f5ea, 0x1dadf43e, 0x233f7061,\n  0x3372f092, 0x8d937e41, 0xd65fecf1, 0x6c223bdb, 0x7cde3759, 0xcbee7460,\n  0x4085f2a7, 0xce77326e, 0xa6078084, 0x19f8509e, 0xe8efd855, 0x61d99735,\n  0xa969a7aa, 0xc50c06c2, 0x5a04abfc, 0x800bcadc, 0x9e447a2e, 0xc3453484,\n  0xfdd56705, 0x0e1e9ec9, 0xdb73dbd3, 0x105588cd, 0x675fda79, 0xe3674340,\n  0xc5c43465, 0x713e38d8, 0x3d28f89e, 0xf16dff20, 0x153e21e7, 0x8fb03d4a,\n  0xe6e39f2b, 0xdb83adf7, 0xe93d5a68, 0x948140f7, 0xf64c261c, 0x94692934,\n  0x411520f7, 0x7602d4f7, 0xbcf46b2e, 0xd4a20068, 0xd4082471, 0x3320f46a,\n  0x43b7d4b7, 0x500061af, 0x1e39f62e, 0x97244546, 0x14214f74, 0xbf8b8840,\n  0x4d95fc1d, 0x96b591af, 0x70f4ddd3, 0x66a02f45, 0xbfbc09ec, 0x03bd9785,\n  0x7fac6dd0, 0x31cb8504, 0x96eb27b3, 0x55fd3941, 0xda2547e6, 0xabca0a9a,\n  0x28507825, 0x530429f4, 0x0a2c86da, 0xe9b66dfb, 0x68dc1462, 0xd7486900,\n  0x680ec0a4, 0x27a18dee, 0x4f3ffea2, 0xe887ad8c, 0xb58ce006, 0x7af4d6b6,\n  0xaace1e7c, 0xd3375fec, 0xce78a399, 0x406b2a42, 0x20fe9e35, 0xd9f385b9,\n  0xee39d7ab, 0x3b124e8b, 0x1dc9faf7, 0x4b6d1856, 0x26a36631, 0xeae397b2,\n  0x3a6efa74, 0xdd5b4332, 0x6841e7f7, 0xca7820fb, 0xfb0af54e, 0xd8feb397,\n  0x454056ac, 0xba489527, 0x55533a3a, 0x20838d87, 0xfe6ba9b7, 0xd096954b,\n  0x55a867bc, 0xa1159a58, 0xcca92963, 0x99e1db33, 0xa62a4a56, 0x3f3125f9,\n  0x5ef47e1c, 0x9029317c, 0xfdf8e802, 0x04272f70, 0x80bb155c, 0x05282ce3,\n  0x95c11548, 0xe4c66d22, 0x48c1133f, 0xc70f86dc, 0x07f9c9ee, 0x41041f0f,\n  0x404779a4, 0x5d886e17, 0x325f51eb, 0xd59bc0d1, 0xf2bcc18f, 0x41113564,\n  0x257b7834, 0x602a9c60, 0xdff8e8a3, 0x1f636c1b, 0x0e12b4c2, 0x02e1329e,\n  0xaf664fd1, 0xcad18115, 0x6b2395e0, 0x333e92e1, 0x3b240b62, 0xeebeb922,\n  0x85b2a20e, 0xe6ba0d99, 0xde720c8c, 0x2da2f728, 0xd0127845, 0x95b794fd,\n  0x647d0862, 0xe7ccf5f0, 0x5449a36f, 0x877d48fa, 0xc39dfd27, 0xf33e8d1e,\n  0x0a476341, 0x992eff74, 0x3a6f6eab, 0xf4f8fd37, 0xa812dc60, 0xa1ebddf8,\n  0x991be14c, 0xdb6e6b0d, 0xc67b5510, 0x6d672c37, 0x2765d43b, 0xdcd0e804,\n  0xf1290dc7, 0xcc00ffa3, 0xb5390f92, 0x690fed0b, 0x667b9ffb, 0xcedb7d9c,\n  0xa091cf0b, 0xd9155ea3, 0xbb132f88, 0x515bad24, 0x7b9479bf, 0x763bd6eb,\n  0x37392eb3, 0xcc115979, 0x8026e297, 0xf42e312d, 0x6842ada7, 0xc66a2b3b,\n  0x12754ccc, 0x782ef11c, 0x6a124237, 0xb79251e7, 0x06a1bbe6, 0x4bfb6350,\n  0x1a6b1018, 0x11caedfa, 0x3d25bdd8, 0xe2e1c3c9, 0x44421659, 0x0a121386,\n  0xd90cec6e, 0xd5abea2a, 0x64af674e, 0xda86a85f, 0xbebfe988, 0x64e4c3fe,\n  0x9dbc8057, 0xf0f7c086, 0x60787bf8, 0x6003604d, 0xd1fd8346, 0xf6381fb0,\n  0x7745ae04, 0xd736fccc, 0x83426b33, 0xf01eab71, 0xb0804187, 0x3c005e5f,\n  0x77a057be, 0xbde8ae24, 0x55464299, 0xbf582e61, 0x4e58f48f, 0xf2ddfda2,\n  0xf474ef38, 0x8789bdc2, 0x5366f9c3, 0xc8b38e74, 0xb475f255, 0x46fcd9b9,\n  0x7aeb2661, 0x8b1ddf84, 0x846a0e79, 0x915f95e2, 0x466e598e, 0x20b45770,\n  0x8cd55591, 0xc902de4c, 0xb90bace1, 0xbb8205d0, 0x11a86248, 0x7574a99e,\n  0xb77f19b6, 0xe0a9dc09, 0x662d09a1, 0xc4324633, 0xe85a1f02, 0x09f0be8c,\n  0x4a99a025, 0x1d6efe10, 0x1ab93d1d, 0x0ba5a4df, 0xa186f20f, 0x2868f169,\n  0xdcb7da83, 0x573906fe, 0xa1e2ce9b, 0x4fcd7f52, 0x50115e01, 0xa70683fa,\n  0xa002b5c4, 0x0de6d027, 0x9af88c27, 0x773f8641, 0xc3604c06, 0x61a806b5,\n  0xf0177a28, 0xc0f586e0, 0x006058aa, 0x30dc7d62, 0x11e69ed7, 0x2338ea63,\n  0x53c2dd94, 0xc2c21634, 0xbbcbee56, 0x90bcb6de, 0xebfc7da1, 0xce591d76,\n  0x6f05e409, 0x4b7c0188, 0x39720a3d, 0x7c927c24, 0x86e3725f, 0x724d9db9,\n  0x1ac15bb4, 0xd39eb8fc, 0xed545578, 0x08fca5b5, 0xd83d7cd3, 0x4dad0fc4,\n  0x1e50ef5e, 0xb161e6f8, 0xa28514d9, 0x6c51133c, 0x6fd5c7e7, 0x56e14ec4,\n  0x362abfce, 0xddc6c837, 0xd79a3234, 0x92638212, 0x670efa8e, 0x406000e0,\n  0x3a39ce37, 0xd3faf5cf, 0xabc27737, 0x5ac52d1b, 0x5cb0679e, 0x4fa33742,\n  0xd3822740, 0x99bc9bbe, 0xd5118e9d, 0xbf0f7315, 0xd62d1c7e, 0xc700c47b,\n  0xb78c1b6b, 0x21a19045, 0xb26eb1be, 0x6a366eb4, 0x5748ab2f, 0xbc946e79,\n  0xc6a376d2, 0x6549c2c8, 0x530ff8ee, 0x468dde7d, 0xd5730a1d, 0x4cd04dc6,\n  0x2939bbdb, 0xa9ba4650, 0xac9526e8, 0xbe5ee304, 0xa1fad5f0, 0x6a2d519a,\n  0x63ef8ce2, 0x9a86ee22, 0xc089c2b8, 0x43242ef6, 0xa51e03aa, 0x9cf2d0a4,\n  0x83c061ba, 0x9be96a4d, 0x8fe51550, 0xba645bd6, 0x2826a2f9, 0xa73a3ae1,\n  0x4ba99586, 0xef5562e9, 0xc72fefd3, 0xf752f7da, 0x3f046f69, 0x77fa0a59,\n  0x80e4a915, 0x87b08601, 0x9b09e6ad, 0x3b3ee593, 0xe990fd5a, 0x9e34d797,\n  0x2cf0b7d9, 0x022b8b51, 0x96d5ac3a, 0x017da67d, 0xd1cf3ed6, 0x7c7d2d28,\n  0x1f9f25cf, 0xadf2b89b, 0x5ad6b472, 0x5a88f54c, 0xe029ac71, 0xe019a5e6,\n  0x47b0acfd, 0xed93fa9b, 0xe8d3c48d, 0x283b57cc, 0xf8d56629, 0x79132e28,\n  0x785f0191, 0xed756055, 0xf7960e44, 0xe3d35e8c, 0x15056dd4, 0x88f46dba,\n  0x03a16125, 0x0564f0bd, 0xc3eb9e15, 0x3c9057a2, 0x97271aec, 0xa93a072a,\n  0x1b3f6d9b, 0x1e6321f5, 0xf59c66fb, 0x26dcf319, 0x7533d928, 0xb155fdf5,\n  0x03563482, 0x8aba3cbb, 0x28517711, 0xc20ad9f8, 0xabcc5167, 0xccad925f,\n  0x4de81751, 0x3830dc8e, 0x379d5862, 0x9320f991, 0xea7a90c2, 0xfb3e7bce,\n  0x5121ce64, 0x774fbe32, 0xa8b6e37e, 0xc3293d46, 0x48de5369, 0x6413e680,\n  0xa2ae0810, 0xdd6db224, 0x69852dfd, 0x09072166, 0xb39a460a, 0x6445c0dd,\n  0x586cdecf, 0x1c20c8ae, 0x5bbef7dd, 0x1b588d40, 0xccd2017f, 0x6bb4e3bb,\n  0xdda26a7e, 0x3a59ff45, 0x3e350a44, 0xbcb4cdd5, 0x72eacea8, 0xfa6484bb,\n  0x8d6612ae, 0xbf3c6f47, 0xd29be463, 0x542f5d9e, 0xaec2771b, 0xf64e6370,\n  0x740e0d8d, 0xe75b1357, 0xf8721671, 0xaf537d5d, 0x4040cb08, 0x4eb4e2cc,\n  0x34d2466a, 0x0115af84, 0xe1b00428, 0x95983a1d, 0x06b89fb4, 0xce6ea048,\n  0x6f3f3b82, 0x3520ab82, 0x011a1d4b, 0x277227f8, 0x611560b1, 0xe7933fdc,\n  0xbb3a792b, 0x344525bd, 0xa08839e1, 0x51ce794b, 0x2f32c9b7, 0xa01fbac9,\n  0xe01cc87e, 0xbcc7d1f6, 0xcf0111c3, 0xa1e8aac7, 0x1a908749, 0xd44fbd9a,\n  0xd0dadecb, 0xd50ada38, 0x0339c32a, 0xc6913667, 0x8df9317c, 0xe0b12b4f,\n  0xf79e59b7, 0x43f5bb3a, 0xf2d519ff, 0x27d9459c, 0xbf97222c, 0x15e6fc2a,\n  0x0f91fc71, 0x9b941525, 0xfae59361, 0xceb69ceb, 0xc2a86459, 0x12baa8d1,\n  0xb6c1075e, 0xe3056a0c, 0x10d25065, 0xcb03a442, 0xe0ec6e0e, 0x1698db3b,\n  0x4c98a0be, 0x3278e964, 0x9f1f9532, 0xe0d392df, 0xd3a0342b, 0x8971f21e,\n  0x1b0a7441, 0x4ba3348c, 0xc5be7120, 0xc37632d8, 0xdf359f8d, 0x9b992f2e,\n  0xe60b6f47, 0x0fe3f11d, 0xe54cda54, 0x1edad891, 0xce6279cf, 0xcd3e7e6f,\n  0x1618b166, 0xfd2c1d05, 0x848fd2c5, 0xf6fb2299, 0xf523f357, 0xa6327623,\n  0x93a83531, 0x56cccd02, 0xacf08162, 0x5a75ebb5, 0x6e163697, 0x88d273cc,\n  0xde966292, 0x81b949d0, 0x4c50901b, 0x71c65614, 0xe6c6c7bd, 0x327a140a,\n  0x45e1d006, 0xc3f27b9a, 0xc9aa53fd, 0x62a80f00, 0xbb25bfe2, 0x35bdd2f6,\n  0x71126905, 0xb2040222, 0xb6cbcf7c, 0xcd769c2b, 0x53113ec0, 0x1640e3d3,\n  0x38abbd60, 0x2547adf0, 0xba38209c, 0xf746ce76, 0x77afa1c5, 0x20756060,\n  0x85cbfe4e, 0x8ae88dd8, 0x7aaaf9b0, 0x4cf9aa7e, 0x1948c25c, 0x02fb8a8c,\n  0x01c36ae4, 0xd6ebe1f9, 0x90d4f869, 0xa65cdea0, 0x3f09252d, 0xc208e69f,\n  0xb74e6132, 0xce77e25b, 0x578fdfe3, 0x3ac372e6,\n];\n\n/**\n * @type {Array.<number>}\n * @const\n * @inner\n */\nvar C_ORIG = [\n  0x4f727068, 0x65616e42, 0x65686f6c, 0x64657253, 0x63727944, 0x6f756274,\n];\n\n/**\n * @param {Array.<number>} lr\n * @param {number} off\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @returns {Array.<number>}\n * @inner\n */\nfunction _encipher(lr, off, P, S) {\n  // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n  var n,\n    l = lr[off],\n    r = lr[off + 1];\n\n  l ^= P[0];\n\n  /*\n    for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n        // Feistel substitution on left word\n        n  = S[l >>> 24],\n        n += S[0x100 | ((l >> 16) & 0xff)],\n        n ^= S[0x200 | ((l >> 8) & 0xff)],\n        n += S[0x300 | (l & 0xff)],\n        r ^= n ^ P[++i],\n        // Feistel substitution on right word\n        n  = S[r >>> 24],\n        n += S[0x100 | ((r >> 16) & 0xff)],\n        n ^= S[0x200 | ((r >> 8) & 0xff)],\n        n += S[0x300 | (r & 0xff)],\n        l ^= n ^ P[++i];\n    */\n\n  //The following is an unrolled version of the above loop.\n  //Iteration 0\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[1];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[2];\n  //Iteration 1\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[3];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[4];\n  //Iteration 2\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[5];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[6];\n  //Iteration 3\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[7];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[8];\n  //Iteration 4\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[9];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[10];\n  //Iteration 5\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[11];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[12];\n  //Iteration 6\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[13];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[14];\n  //Iteration 7\n  n = S[l >>> 24];\n  n += S[0x100 | ((l >> 16) & 0xff)];\n  n ^= S[0x200 | ((l >> 8) & 0xff)];\n  n += S[0x300 | (l & 0xff)];\n  r ^= n ^ P[15];\n  n = S[r >>> 24];\n  n += S[0x100 | ((r >> 16) & 0xff)];\n  n ^= S[0x200 | ((r >> 8) & 0xff)];\n  n += S[0x300 | (r & 0xff)];\n  l ^= n ^ P[16];\n\n  lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n  lr[off + 1] = l;\n  return lr;\n}\n\n/**\n * @param {Array.<number>} data\n * @param {number} offp\n * @returns {{key: number, offp: number}}\n * @inner\n */\nfunction _streamtoword(data, offp) {\n  for (var i = 0, word = 0; i < 4; ++i)\n    (word = (word << 8) | (data[offp] & 0xff)),\n      (offp = (offp + 1) % data.length);\n  return { key: word, offp: offp };\n}\n\n/**\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _key(key, P, S) {\n  var offset = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offset)),\n      (offset = sw.offp),\n      (P[i] = P[i] ^ sw.key);\n  for (i = 0; i < plen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (P[i] = lr[0]), (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (lr = _encipher(lr, 0, P, S)), (S[i] = lr[0]), (S[i + 1] = lr[1]);\n}\n\n/**\n * Expensive key schedule Blowfish.\n * @param {Array.<number>} data\n * @param {Array.<number>} key\n * @param {Array.<number>} P\n * @param {Array.<number>} S\n * @inner\n */\nfunction _ekskey(data, key, P, S) {\n  var offp = 0,\n    lr = [0, 0],\n    plen = P.length,\n    slen = S.length,\n    sw;\n  for (var i = 0; i < plen; i++)\n    (sw = _streamtoword(key, offp)), (offp = sw.offp), (P[i] = P[i] ^ sw.key);\n  offp = 0;\n  for (i = 0; i < plen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (P[i] = lr[0]),\n      (P[i + 1] = lr[1]);\n  for (i = 0; i < slen; i += 2)\n    (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[0] ^= sw.key),\n      (sw = _streamtoword(data, offp)),\n      (offp = sw.offp),\n      (lr[1] ^= sw.key),\n      (lr = _encipher(lr, 0, P, S)),\n      (S[i] = lr[0]),\n      (S[i + 1] = lr[1]);\n}\n\n/**\n * Internaly crypts a string.\n * @param {Array.<number>} b Bytes to crypt\n * @param {Array.<number>} salt Salt bytes to use\n * @param {number} rounds Number of rounds\n * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n *  omitted, the operation will be performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _crypt(b, salt, rounds, callback, progressCallback) {\n  var cdata = C_ORIG.slice(),\n    clen = cdata.length,\n    err;\n\n  // Validate\n  if (rounds < 4 || rounds > 31) {\n    err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.length !== BCRYPT_SALT_LEN) {\n    err = Error(\n      \"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN,\n    );\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  rounds = (1 << rounds) >>> 0;\n\n  var P,\n    S,\n    i = 0,\n    j;\n\n  //Use typed arrays when available - huge speedup!\n  if (typeof Int32Array === \"function\") {\n    P = new Int32Array(P_ORIG);\n    S = new Int32Array(S_ORIG);\n  } else {\n    P = P_ORIG.slice();\n    S = S_ORIG.slice();\n  }\n\n  _ekskey(salt, b, P, S);\n\n  /**\n   * Calcualtes the next round.\n   * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n   * @inner\n   */\n  function next() {\n    if (progressCallback) progressCallback(i / rounds);\n    if (i < rounds) {\n      var start = Date.now();\n      for (; i < rounds; ) {\n        i = i + 1;\n        _key(b, P, S);\n        _key(salt, P, S);\n        if (Date.now() - start > MAX_EXECUTION_TIME) break;\n      }\n    } else {\n      for (i = 0; i < 64; i++)\n        for (j = 0; j < clen >> 1; j++) _encipher(cdata, j << 1, P, S);\n      var ret = [];\n      for (i = 0; i < clen; i++)\n        ret.push(((cdata[i] >> 24) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 16) & 0xff) >>> 0),\n          ret.push(((cdata[i] >> 8) & 0xff) >>> 0),\n          ret.push((cdata[i] & 0xff) >>> 0);\n      if (callback) {\n        callback(null, ret);\n        return;\n      } else return ret;\n    }\n    if (callback) nextTick(next);\n  }\n\n  // Async\n  if (typeof callback !== \"undefined\") {\n    next();\n\n    // Sync\n  } else {\n    var res;\n    while (true) if (typeof (res = next()) !== \"undefined\") return res || [];\n  }\n}\n\n/**\n * Internally hashes a password.\n * @param {string} password Password to hash\n * @param {?string} salt Salt to use, actually never null\n * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n *  hashing is performed synchronously.\n *  @param {function(number)=} progressCallback Callback called with the current progress\n * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n * @inner\n */\nfunction _hash(password, salt, callback, progressCallback) {\n  var err;\n  if (typeof password !== \"string\" || typeof salt !== \"string\") {\n    err = Error(\"Invalid string / salt: Not a string\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n\n  // Validate the salt\n  var minor, offset;\n  if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n    err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  if (salt.charAt(2) === \"$\") (minor = String.fromCharCode(0)), (offset = 3);\n  else {\n    minor = salt.charAt(2);\n    if (\n      (minor !== \"a\" && minor !== \"b\" && minor !== \"y\") ||\n      salt.charAt(3) !== \"$\"\n    ) {\n      err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n      if (callback) {\n        nextTick(callback.bind(this, err));\n        return;\n      } else throw err;\n    }\n    offset = 4;\n  }\n\n  // Extract number of rounds\n  if (salt.charAt(offset + 2) > \"$\") {\n    err = Error(\"Missing salt rounds\");\n    if (callback) {\n      nextTick(callback.bind(this, err));\n      return;\n    } else throw err;\n  }\n  var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10,\n    r2 = parseInt(salt.substring(offset + 1, offset + 2), 10),\n    rounds = r1 + r2,\n    real_salt = salt.substring(offset + 3, offset + 25);\n  password += minor >= \"a\" ? \"\\x00\" : \"\";\n\n  var passwordb = utf8Array(password),\n    saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n\n  /**\n   * Finishes hashing.\n   * @param {Array.<number>} bytes Byte array\n   * @returns {string}\n   * @inner\n   */\n  function finish(bytes) {\n    var res = [];\n    res.push(\"$2\");\n    if (minor >= \"a\") res.push(minor);\n    res.push(\"$\");\n    if (rounds < 10) res.push(\"0\");\n    res.push(rounds.toString());\n    res.push(\"$\");\n    res.push(base64_encode(saltb, saltb.length));\n    res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n    return res.join(\"\");\n  }\n\n  // Sync\n  if (typeof callback == \"undefined\")\n    return finish(_crypt(passwordb, saltb, rounds));\n  // Async\n  else {\n    _crypt(\n      passwordb,\n      saltb,\n      rounds,\n      function (err, bytes) {\n        if (err) callback(err, null);\n        else callback(null, finish(bytes));\n      },\n      progressCallback,\n    );\n  }\n}\n\n/**\n * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n * @function\n * @param {!Array.<number>} bytes Byte array\n * @param {number} length Maximum input length\n * @returns {string}\n */\nfunction encodeBase64(bytes, length) {\n  return base64_encode(bytes, length);\n}\n\n/**\n * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n * @function\n * @param {string} string String to decode\n * @param {number} length Maximum output length\n * @returns {!Array.<number>}\n */\nfunction decodeBase64(string, length) {\n  return base64_decode(string, length);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  setRandomFallback,\n  genSaltSync,\n  genSalt,\n  hashSync,\n  hash,\n  compareSync,\n  compare,\n  getRounds,\n  getSalt,\n  truncates,\n  encodeBase64,\n  decodeBase64,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/index.js\n");

/***/ })

};
;
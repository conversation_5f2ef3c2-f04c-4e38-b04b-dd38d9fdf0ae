import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files, error pages, and API routes that don't need tenant context
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/api/health') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/public/') ||
    pathname.startsWith('/tenant-not-found') ||
    pathname.startsWith('/tenant-inactive') ||
    pathname.startsWith('/error') ||
    pathname.match(/\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/)
  ) {
    return NextResponse.next();
  }

  try {
    // Simple tenant resolution for Edge Runtime
    const host = request.headers.get('host') || '';
    const parts = host.split('.');

    let tenantSubdomain = 'default';
    let tenantName = 'GymD Platform';

    // For localhost development
    if (host.includes('localhost')) {
      const subdomain = parts[0].split(':')[0]; // Remove port from subdomain
      if (subdomain !== 'localhost') {
        tenantSubdomain = subdomain;
        tenantName = `${subdomain} Gym`;
      }
    } else if (parts.length >= 3) {
      // For production domains
      tenantSubdomain = parts[0];
      tenantName = `${parts[0]} Gym`;
    }

    // Clone the request headers and add tenant information
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-tenant-subdomain', tenantSubdomain);
    requestHeaders.set('x-tenant-name', tenantName);

    // Create response with tenant headers
    const response = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });

    // Add tenant info to response headers for client-side access
    response.headers.set('x-tenant-subdomain', tenantSubdomain);
    response.headers.set('x-tenant-name', tenantName);

    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * But include API routes that need tenant context
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};

import { z } from 'zod';

// Common schemas
export const emailSchema = z.string().email('Invalid email address');
export const phoneSchema = z.string().min(10, 'Phone number must be at least 10 digits');
export const passwordSchema = z.string().min(8, 'Password must be at least 8 characters');

// Gym schema
export const gymSchema = z.object({
  name: z.string().min(1, 'Gym name is required').max(100, 'Gym name too long'),
  subdomain: z.string()
    .min(3, 'Subdomain must be at least 3 characters')
    .max(50, 'Subdomain too long')
    .regex(/^[a-z0-9-]+$/, 'Subdomain can only contain lowercase letters, numbers, and hyphens'),
  description: z.string().max(500, 'Description too long').optional(),
  address: z.object({
    street: z.string().min(1, 'Street address is required'),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(1, 'State is required'),
    zipCode: z.string().min(1, 'ZIP code is required'),
    country: z.string().default('US'),
  }),
  contact: z.object({
    phone: phoneSchema,
    email: emailSchema,
    website: z.string().url('Invalid website URL').optional(),
  }),
});

// Package schema
export const packageSchema = z.object({
  gymId: z.string().min(1, 'Gym ID is required'),
  name: z.string().min(1, 'Package name is required').max(100, 'Package name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  type: z.enum(['monthly', 'quarterly', 'yearly', 'custom']),
  duration: z.object({
    value: z.number().min(1, 'Duration value must be at least 1'),
    unit: z.enum(['days', 'weeks', 'months', 'years']),
  }),
  pricing: z.object({
    amount: z.number().min(0, 'Amount must be non-negative'),
    currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),
    setupFee: z.number().min(0, 'Setup fee must be non-negative').optional(),
  }),
  features: z.array(z.string()).default([]),
  benefits: z.array(z.string()).default([]),
  restrictions: z.object({
    maxClasses: z.number().min(0).optional(),
    maxTrainerSessions: z.number().min(0).optional(),
    accessHours: z.object({
      start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
      end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    }).optional(),
    allowedDays: z.array(z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])).optional(),
  }).optional(),
  isActive: z.boolean().default(true),
  isPopular: z.boolean().default(false),
  sortOrder: z.number().default(0),
});

// Member schema
export const memberSchema = z.object({
  personalInfo: z.object({
    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
    email: emailSchema,
    phone: phoneSchema,
    dateOfBirth: z.string().transform((str) => new Date(str)).optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    profilePhoto: z.string().url('Invalid photo URL').optional(),
  }),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().default('US'),
  }).optional(),
  emergencyContact: z.object({
    name: z.string().min(1, 'Emergency contact name is required'),
    relationship: z.string().min(1, 'Relationship is required'),
    phone: phoneSchema,
  }),
  healthInfo: z.object({
    medicalConditions: z.array(z.string()).default([]),
    allergies: z.array(z.string()).default([]),
    medications: z.array(z.string()).default([]),
    fitnessGoals: z.array(z.string()).default([]),
    notes: z.string().max(1000, 'Notes too long').optional(),
  }).optional(),
  preferences: z.object({
    preferredTrainers: z.array(z.string()).default([]),
    notifications: z.object({
      email: z.boolean().default(true),
      sms: z.boolean().default(false),
      whatsapp: z.boolean().default(false),
    }),
    language: z.string().default('en'),
  }).optional(),
});

// Trainer schema
export const trainerSchema = z.object({
  personalInfo: z.object({
    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
    email: emailSchema,
    phone: phoneSchema,
    dateOfBirth: z.string().transform((str) => new Date(str)).optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    profilePhoto: z.string().url('Invalid photo URL').optional(),
  }),
  professional: z.object({
    specializations: z.array(z.string()).default([]),
    certifications: z.array(z.object({
      name: z.string().min(1, 'Certification name is required'),
      issuedBy: z.string().min(1, 'Issuing organization is required'),
      issuedDate: z.string().transform((str) => new Date(str)),
      expiryDate: z.string().transform((str) => new Date(str)).optional(),
      certificateUrl: z.string().url('Invalid certificate URL').optional(),
    })).default([]),
    experience: z.number().min(0, 'Experience must be non-negative').default(0),
    bio: z.string().max(1000, 'Bio too long').optional(),
    hourlyRate: z.number().min(0, 'Hourly rate must be non-negative').optional(),
  }),
  employment: z.object({
    hireDate: z.string().transform((str) => new Date(str)).default(() => new Date()),
    employmentType: z.enum(['full-time', 'part-time', 'contract', 'freelance']),
    salary: z.number().min(0, 'Salary must be non-negative').optional(),
  }),
});

// Subscription schema
export const subscriptionSchema = z.object({
  memberId: z.string().min(1, 'Member ID is required'),
  packageId: z.string().min(1, 'Package ID is required'),
  startDate: z.string().transform((str) => new Date(str)),
  payment: z.object({
    amount: z.number().min(0, 'Amount must be non-negative'),
    currency: z.string().length(3, 'Currency must be 3 characters').default('USD'),
    setupFee: z.number().min(0, 'Setup fee must be non-negative').optional(),
    paymentMethod: z.enum(['cash', 'card', 'bank_transfer', 'online', 'other']),
    transactionId: z.string().optional(),
  }),
  autoRenewal: z.object({
    enabled: z.boolean().default(false),
  }).optional(),
  notes: z.string().max(500, 'Notes too long').optional(),
});

// API query schemas
export const paginationSchema = z.object({
  page: z.string().transform((val) => parseInt(val, 10)).default('1'),
  limit: z.string().transform((val) => parseInt(val, 10)).default('10'),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const idParamSchema = z.object({
  id: z.string().min(1, 'ID is required'),
});

// User role enum
export const userRoleSchema = z.enum(['super_admin', 'gym_owner', 'gym_admin', 'gym_staff', 'member']);

// User status enum
export const userStatusSchema = z.enum(['active', 'inactive', 'suspended', 'pending']);

// User registration schema
export const userRegistrationSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  confirmPassword: z.string(),
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number').optional(),
  role: userRoleSchema.default('member'),
  gymId: z.string().optional(),
  dateOfBirth: z.string().datetime().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().default('US')
  }).optional()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// User login schema
export const userLoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  gymSubdomain: z.string().optional(),
  rememberMe: z.boolean().default(false)
});

// User profile update schema
export const userProfileUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long').optional(),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long').optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number').optional(),
  avatar: z.string().url('Invalid avatar URL').optional(),
  dateOfBirth: z.string().datetime().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional()
  }).optional(),
  preferences: z.object({
    notifications: z.object({
      email: z.boolean().optional(),
      sms: z.boolean().optional(),
      whatsapp: z.boolean().optional()
    }).optional(),
    language: z.string().optional(),
    timezone: z.string().optional()
  }).optional()
});

// User admin update schema (for admin operations)
export const userAdminUpdateSchema = userProfileUpdateSchema.extend({
  role: userRoleSchema.optional(),
  status: userStatusSchema.optional(),
  gymId: z.string().optional(),
  gymIds: z.array(z.string()).optional()
});

// Password change schema
export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  confirmNewPassword: z.string()
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: "New passwords don't match",
  path: ["confirmNewPassword"],
});

// Password reset request schema
export const passwordResetRequestSchema = z.object({
  email: z.string().email('Invalid email address')
});

// Password reset schema
export const passwordResetSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Email verification schema
export const emailVerificationSchema = z.object({
  token: z.string().min(1, 'Verification token is required')
});

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { ApiResponse, PaginatedResponse } from '@/types';

/**
 * Create a success response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  metadata?: any,
  status: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      message,
      ...metadata
    },
    { status }
  );
}

/**
 * Create an error response
 */
export function createErrorResponse(
  error: string,
  status: number = 400,
  details?: any
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error,
      ...(details && { details }),
    },
    { status }
  );
}

/**
 * Create a paginated response
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): NextResponse<PaginatedResponse<T>> {
  const totalPages = Math.ceil(total / limit);

  return NextResponse.json({
    success: true,
    data,
    message,
    pagination: {
      page,
      limit,
      total,
      totalPages,
    },
  });
}

/**
 * Handle validation errors from Zod
 */
export function handleValidationError(error: ZodError): NextResponse<ApiResponse> {
  const formattedErrors = error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
  }));

  return createErrorResponse(
    'Validation failed',
    400,
    { validationErrors: formattedErrors }
  );
}

/**
 * Handle database errors
 */
export function handleDatabaseError(error: any): NextResponse<ApiResponse> {
  console.error('Database error:', error);

  // Handle MongoDB duplicate key error
  if (error.code === 11000) {
    const field = Object.keys(error.keyPattern || {})[0] || 'field';
    return createErrorResponse(`${field} already exists`, 409);
  }

  // Handle validation errors
  if (error.name === 'ValidationError') {
    const validationErrors = Object.values(error.errors).map((err: any) => ({
      field: err.path,
      message: err.message,
    }));
    return createErrorResponse('Validation failed', 400, { validationErrors });
  }

  // Handle cast errors (invalid ObjectId, etc.)
  if (error.name === 'CastError') {
    return createErrorResponse('Invalid ID format', 400);
  }

  // Generic database error
  return createErrorResponse('Database operation failed', 500);
}

/**
 * Handle async route errors
 */
export function withErrorHandling(
  handler: (request: Request, context?: any) => Promise<NextResponse>
) {
  return async (request: Request, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API route error:', error);

      if (error instanceof ZodError) {
        return handleValidationError(error);
      }

      if (error && typeof error === 'object' && 'code' in error) {
        return handleDatabaseError(error);
      }

      return createErrorResponse(
        error instanceof Error ? error.message : 'Internal server error',
        500
      );
    }
  };
}

/**
 * Extract pagination parameters from URL search params
 */
export function extractPaginationParams(searchParams: URLSearchParams) {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10', 10)));
  const search = searchParams.get('search') || undefined;
  const sortBy = searchParams.get('sortBy') || 'createdAt';
  const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';

  return {
    page,
    limit,
    skip: (page - 1) * limit,
    search,
    sortBy,
    sortOrder,
  };
}

/**
 * Build MongoDB sort object
 */
export function buildSortObject(sortBy: string, sortOrder: 'asc' | 'desc') {
  return { [sortBy]: sortOrder === 'asc' ? 1 : -1 };
}

/**
 * Build MongoDB search filter
 */
export function buildSearchFilter(search: string | undefined, fields: string[]) {
  if (!search) return {};

  const searchRegex = new RegExp(search, 'i');
  return {
    $or: fields.map((field) => ({ [field]: searchRegex })),
  };
}

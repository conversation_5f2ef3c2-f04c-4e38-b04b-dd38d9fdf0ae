import { getServerSession } from 'next-auth/next';
import { authOptions } from './config';
import { UserRole, UserStatus } from '@/models/User';
import { NextRequest } from 'next/server';

// Get current session server-side
export async function getCurrentSession() {
  return await getServerSession(authOptions);
}

// Get current user from session
export async function getCurrentUser() {
  const session = await getCurrentSession();
  return session?.user || null;
}

// Check if user is authenticated
export async function isAuthenticated(): Promise<boolean> {
  const session = await getCurrentSession();
  return !!session?.user && session.user.status === UserStatus.ACTIVE;
}

// Check if user has specific role
export async function hasRole(role: UserRole): Promise<boolean> {
  const user = await getCurrentUser();
  return user?.role === role;
}

// Check if user has any of the specified roles
export async function hasAnyRole(roles: UserRole[]): Promise<boolean> {
  const user = await getCurrentUser();
  return user ? roles.includes(user.role) : false;
}

// Check if user is super admin
export async function isSuperAdmin(): Promise<boolean> {
  return await hasRole(UserRole.SUPER_ADMIN);
}

// Check if user is gym owner
export async function isGymOwner(): Promise<boolean> {
  return await hasRole(UserRole.GYM_OWNER);
}

// Check if user is gym admin
export async function isGymAdmin(): Promise<boolean> {
  return await hasRole(UserRole.GYM_ADMIN);
}

// Check if user is gym staff
export async function isGymStaff(): Promise<boolean> {
  return await hasRole(UserRole.GYM_STAFF);
}

// Check if user is member
export async function isMember(): Promise<boolean> {
  return await hasRole(UserRole.MEMBER);
}

// Check if user has admin privileges (super admin, gym owner, or gym admin)
export async function hasAdminPrivileges(): Promise<boolean> {
  return await hasAnyRole([UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN]);
}

// Check if user has staff privileges (admin privileges or gym staff)
export async function hasStaffPrivileges(): Promise<boolean> {
  return await hasAnyRole([
    UserRole.SUPER_ADMIN, 
    UserRole.GYM_OWNER, 
    UserRole.GYM_ADMIN, 
    UserRole.GYM_STAFF
  ]);
}

// Check if user has access to specific gym
export async function hasGymAccess(gymId: string): Promise<boolean> {
  const user = await getCurrentUser();
  
  if (!user) return false;
  
  // Super admin has access to all gyms
  if (user.role === UserRole.SUPER_ADMIN) return true;
  
  // Check if user's primary gym matches
  if (user.gymId === gymId) return true;
  
  // Check if user has access to multiple gyms
  if (user.gymIds && user.gymIds.includes(gymId)) return true;
  
  return false;
}

// Get user's accessible gym IDs
export async function getAccessibleGymIds(): Promise<string[]> {
  const user = await getCurrentUser();
  
  if (!user) return [];
  
  // Super admin has access to all gyms (return empty array to indicate all)
  if (user.role === UserRole.SUPER_ADMIN) return [];
  
  // Collect all accessible gym IDs
  const gymIds: string[] = [];
  
  if (user.gymId) gymIds.push(user.gymId);
  if (user.gymIds) gymIds.push(...user.gymIds);
  
  // Remove duplicates
  return [...new Set(gymIds)];
}

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  [UserRole.SUPER_ADMIN]: 5,
  [UserRole.GYM_OWNER]: 4,
  [UserRole.GYM_ADMIN]: 3,
  [UserRole.GYM_STAFF]: 2,
  [UserRole.MEMBER]: 1
};

// Check if user has higher or equal role level
export async function hasMinimumRole(minimumRole: UserRole): Promise<boolean> {
  const user = await getCurrentUser();
  
  if (!user) return false;
  
  const userLevel = ROLE_HIERARCHY[user.role] || 0;
  const minimumLevel = ROLE_HIERARCHY[minimumRole] || 0;
  
  return userLevel >= minimumLevel;
}

// Authorization error class
export class AuthorizationError extends Error {
  constructor(message: string = 'Unauthorized access') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

// Require authentication (throws error if not authenticated)
export async function requireAuth() {
  const authenticated = await isAuthenticated();
  if (!authenticated) {
    throw new AuthorizationError('Authentication required');
  }
}

// Require specific role (throws error if user doesn't have role)
export async function requireRole(role: UserRole) {
  await requireAuth();
  const hasRequiredRole = await hasRole(role);
  if (!hasRequiredRole) {
    throw new AuthorizationError(`${role} role required`);
  }
}

// Require any of the specified roles
export async function requireAnyRole(roles: UserRole[]) {
  await requireAuth();
  const hasRequiredRole = await hasAnyRole(roles);
  if (!hasRequiredRole) {
    throw new AuthorizationError(`One of the following roles required: ${roles.join(', ')}`);
  }
}

// Require minimum role level
export async function requireMinimumRole(minimumRole: UserRole) {
  await requireAuth();
  const hasMinRole = await hasMinimumRole(minimumRole);
  if (!hasMinRole) {
    throw new AuthorizationError(`Minimum role ${minimumRole} required`);
  }
}

// Require gym access
export async function requireGymAccess(gymId: string) {
  await requireAuth();
  const hasAccess = await hasGymAccess(gymId);
  if (!hasAccess) {
    throw new AuthorizationError('Access to this gym is not authorized');
  }
}

// Require admin privileges
export async function requireAdminPrivileges() {
  await requireAnyRole([UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN]);
}

// Require staff privileges
export async function requireStaffPrivileges() {
  await requireAnyRole([
    UserRole.SUPER_ADMIN, 
    UserRole.GYM_OWNER, 
    UserRole.GYM_ADMIN, 
    UserRole.GYM_STAFF
  ]);
}

// Extract bearer token from request headers
export function extractBearerToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// Validate session status
export function validateSessionStatus(status: UserStatus): boolean {
  return status === UserStatus.ACTIVE;
}

// Get role display name
export function getRoleDisplayName(role: UserRole): string {
  const roleNames = {
    [UserRole.SUPER_ADMIN]: 'Super Administrator',
    [UserRole.GYM_OWNER]: 'Gym Owner',
    [UserRole.GYM_ADMIN]: 'Gym Administrator',
    [UserRole.GYM_STAFF]: 'Gym Staff',
    [UserRole.MEMBER]: 'Member'
  };
  
  return roleNames[role] || role;
}

// Get status display name
export function getStatusDisplayName(status: UserStatus): string {
  const statusNames = {
    [UserStatus.ACTIVE]: 'Active',
    [UserStatus.INACTIVE]: 'Inactive',
    [UserStatus.SUSPENDED]: 'Suspended',
    [UserStatus.PENDING]: 'Pending'
  };
  
  return statusNames[status] || status;
}

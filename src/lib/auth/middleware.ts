import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { UserRole, UserStatus } from '@/models/User';
import { createErrorResponse } from '@/lib/utils/api';

// Authentication middleware options
interface AuthMiddlewareOptions {
  requireAuth?: boolean;
  roles?: UserRole[];
  minimumRole?: UserRole;
  requireGymAccess?: boolean;
  allowSuperAdmin?: boolean;
}

// Enhanced request with user information
export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    gymId?: string;
    gymIds?: string[];
  };
  gym?: {
    id: string;
    name: string;
    subdomain: string;
  };
}

// Role hierarchy for permission checking
const ROLE_HIERARCHY = {
  [UserRole.SUPER_ADMIN]: 5,
  [UserRole.GYM_OWNER]: 4,
  [UserRole.GYM_ADMIN]: 3,
  [UserRole.GYM_STAFF]: 2,
  [UserRole.MEMBER]: 1
};

// Authentication middleware
export function withAuth(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>,
  options: AuthMiddlewareOptions = {}
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      // Get JWT token from request
      const token = await getToken({ 
        req: request, 
        secret: process.env.NEXTAUTH_SECRET 
      });

      // Check if authentication is required
      if (options.requireAuth !== false) {
        if (!token) {
          return createErrorResponse('Authentication required', 401);
        }

        // Check user status
        if (token.status !== UserStatus.ACTIVE) {
          return createErrorResponse('Account is not active', 403);
        }
      }

      // Attach user information to request if authenticated
      const authenticatedRequest = request as AuthenticatedRequest;
      if (token) {
        authenticatedRequest.user = {
          id: token.id,
          email: token.email || '',
          firstName: token.firstName || '',
          lastName: token.lastName || '',
          role: token.role,
          status: token.status,
          gymId: token.gymId,
          gymIds: token.gymIds
        };
      }

      // Role-based authorization
      if (token && options.roles && options.roles.length > 0) {
        if (!options.roles.includes(token.role)) {
          return createErrorResponse('Insufficient permissions', 403);
        }
      }

      // Minimum role authorization
      if (token && options.minimumRole) {
        const userLevel = ROLE_HIERARCHY[token.role] || 0;
        const minimumLevel = ROLE_HIERARCHY[options.minimumRole] || 0;
        
        if (userLevel < minimumLevel) {
          return createErrorResponse('Insufficient role level', 403);
        }
      }

      // Gym access authorization
      if (token && options.requireGymAccess) {
        const gymId = getGymIdFromRequest(request);
        
        if (gymId && !hasGymAccess(token, gymId)) {
          return createErrorResponse('Access to this gym is not authorized', 403);
        }
      }

      // Call the original handler
      return await handler(authenticatedRequest, context);

    } catch (error) {
      console.error('Authentication middleware error:', error);
      return createErrorResponse('Authentication error', 500);
    }
  };
}

// Helper function to check gym access
function hasGymAccess(token: any, gymId: string): boolean {
  // Super admin has access to all gyms
  if (token.role === UserRole.SUPER_ADMIN) return true;
  
  // Check if user's primary gym matches
  if (token.gymId === gymId) return true;
  
  // Check if user has access to multiple gyms
  if (token.gymIds && token.gymIds.includes(gymId)) return true;
  
  return false;
}

// Helper function to extract gym ID from request
function getGymIdFromRequest(request: NextRequest): string | null {
  // Try to get gymId from query parameters
  const { searchParams } = new URL(request.url);
  const gymIdFromQuery = searchParams.get('gymId');
  if (gymIdFromQuery) return gymIdFromQuery;
  
  // Try to get gymId from headers (set by tenant middleware)
  const gymIdFromHeader = request.headers.get('x-gym-id');
  if (gymIdFromHeader) return gymIdFromHeader;
  
  return null;
}

// Specific middleware functions for common use cases

// Require authentication
export function requireAuth(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, { requireAuth: true });
}

// Require admin privileges
export function requireAdmin(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN]
  });
}

// Require staff privileges
export function requireStaff(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN, UserRole.GYM_STAFF]
  });
}

// Require super admin
export function requireSuperAdmin(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    roles: [UserRole.SUPER_ADMIN]
  });
}

// Require gym owner
export function requireGymOwner(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER]
  });
}

// Require gym access
export function requireGymAccess(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    requireGymAccess: true
  });
}

// Combine authentication with gym access
export function requireAuthAndGymAccess(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    requireGymAccess: true
  });
}

// Admin with gym access
export function requireAdminAndGymAccess(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN],
    requireGymAccess: true
  });
}

// Staff with gym access
export function requireStaffAndGymAccess(
  handler: (request: AuthenticatedRequest, context?: any) => Promise<NextResponse>
) {
  return withAuth(handler, {
    requireAuth: true,
    roles: [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN, UserRole.GYM_STAFF],
    requireGymAccess: true
  });
}

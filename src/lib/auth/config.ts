import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import { connectToDatabase } from '@/lib/database/connection';
import User, { UserRole, UserStatus } from '@/models/User';
import Gym from '@/models/Gym';
import { z } from 'zod';

// Login schema validation
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
  gymSubdomain: z.string().optional()
});

// Extended user type for NextAuth
declare module 'next-auth' {
  interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    gymId?: string;
    gymIds?: string[];
    avatar?: string;
  }

  interface Session {
    user: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      fullName: string;
      role: UserRole;
      status: UserStatus;
      gymId?: string;
      gymIds?: string[];
      avatar?: string;
    };
    gym?: {
      id: string;
      name: string;
      subdomain: string;
      domain: string;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: UserRole;
    status: UserStatus;
    gymId?: string;
    gymIds?: string[];
    gymSubdomain?: string;
  }
}

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(
    connectToDatabase().then(({ db }) => db)
  ),

  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
        gymSubdomain: { label: 'Gym Subdomain', type: 'text' }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            throw new Error('Email and password are required');
          }

          // Validate input
          const validatedCredentials = loginSchema.parse(credentials);

          // Connect to database
          await connectToDatabase();

          // Find user by email
          const user = await User.findOne({
            email: validatedCredentials.email.toLowerCase()
          }).select('+password');

          if (!user) {
            throw new Error('Invalid email or password');
          }

          // Check if user is locked
          if (user.isLocked) {
            throw new Error('Account is temporarily locked due to too many failed login attempts');
          }

          // Check if user status allows login
          if (user.status === UserStatus.SUSPENDED) {
            throw new Error('Account is suspended');
          }

          if (user.status === UserStatus.INACTIVE) {
            throw new Error('Account is inactive');
          }

          // Verify password
          const isPasswordValid = await user.comparePassword(validatedCredentials.password);

          if (!isPasswordValid) {
            // Increment login attempts
            await user.incrementLoginAttempts();
            throw new Error('Invalid email or password');
          }

          // Reset login attempts on successful login
          await user.resetLoginAttempts();

          // Handle gym-specific login
          let selectedGymId = user.gymId;

          if (validatedCredentials.gymSubdomain) {
            // Verify user has access to the specified gym
            let gym;
            // Handle gym resolution with fallback logic
            if (validatedCredentials.gymSubdomain !== 'default') {
              // Specific gym subdomain provided
              gym = await Gym.findOne({
                subdomain: validatedCredentials.gymSubdomain,
                isActive: true
              });
            } else {
              if (!user.gymId) {
                throw new Error('You do not have access to any gym');
              } 
              
              // Default subdomain - try to find user's gym
              gym = await Gym.findById(user.gymId);
            }

            if (!gym) {
              throw new Error('Gym not found');
            }

            // Check if user has access to this gym
            let hasAccess = user.role === UserRole.SUPER_ADMIN ||
              user.gymId === gym._id.toString() ||
              (user.gymIds && user.gymIds.includes(gym._id.toString()));

            // For default subdomain, be more flexible with access
            if (!hasAccess && validatedCredentials.gymSubdomain === 'default') {
              // If user doesn't have a specific gym assigned, allow access to default gym
              if (!user.gymId || user.gymId === gym._id.toString()) {
                hasAccess = true;

                // Update user's gymId to match this gym for future logins
                if (!user.gymId) {
                  await User.findByIdAndUpdate(user._id, { gymId: gym._id });
                  console.log(`Updated user ${user.email} gymId to ${gym._id}`);
                }
              }
            }

            if (!hasAccess) {
              throw new Error('You do not have access to this gym');
            }

            selectedGymId = gym._id.toString();
          }

          // Return user object for NextAuth
          return {
            id: user._id.toString(),
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            status: user.status,
            gymId: selectedGymId,
            gymIds: user.gymIds,
            avatar: user.avatar
          };

        } catch (error) {
          console.error('Authentication error:', error);
          throw new Error(error instanceof Error ? error.message : 'Authentication failed');
        }
      }
    })
  ],

  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },

  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },

  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        token.id = user.id;
        token.role = user.role;
        token.status = user.status;
        token.gymId = user.gymId;
        token.gymIds = user.gymIds;
      }

      return token;
    },

    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.status = token.status;
        session.user.gymId = token.gymId;
        session.user.gymIds = token.gymIds;
        session.user.fullName = `${session.user.firstName} ${session.user.lastName}`;

        // Add gym information to session if user has a selected gym
        if (token.gymId) {
          try {
            await connectToDatabase();
            const gym = await Gym.findById(token.gymId).select('name subdomain domain');

            if (gym) {
              session.gym = {
                id: gym._id.toString(),
                name: gym.name,
                subdomain: gym.subdomain,
                domain: gym.domain
              };
            }
          } catch (error) {
            console.error('Error fetching gym for session:', error);
          }
        }
      }

      return session;
    },

    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`;

      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) return url;

      return baseUrl;
    }
  },

  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/dashboard'
  },

  events: {
    async signIn({ user }) {
      console.log('User signed in:', {
        userId: user.id,
        email: user.email,
        role: user.role,
        gymId: user.gymId
      });
    },

    async signOut({ session, token }) {
      console.log('User signed out:', {
        userId: token?.id || session?.user?.id
      });
    }
  },

  debug: process.env.NODE_ENV === 'development',
};

export default authOptions;

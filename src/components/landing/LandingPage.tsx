'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Dumbbell, 
  Users, 
  Calendar, 
  MessageSquare, 
  TrendingUp, 
  Shield, 
  Zap, 
  Star,
  CheckCircle,
  ArrowRight,
  Building2
} from 'lucide-react';

export function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10" />
        <div className="relative container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4 rounded-2xl">
                <Dumbbell className="h-12 w-12 text-white" />
              </div>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
              GymD Platform
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
              The Ultimate Multi-Tenant Gym Management System
            </p>
            <p className="text-lg text-gray-500 mb-12 max-w-2xl mx-auto">
              Streamline your gym operations, boost member engagement, and grow your fitness business with our comprehensive management platform.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="xl" variant="gradient" className="group">
                Start Your Free Trial
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button size="xl" variant="outline">
                Watch Demo
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Run Your Gym
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Powerful features designed to help gym owners manage their business efficiently and grow their member base.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Users className="h-8 w-8" />}
              title="Member Management"
              description="Complete member profiles, subscription tracking, and automated billing systems."
              gradient="from-blue-500 to-blue-600"
            />
            <FeatureCard
              icon={<Calendar className="h-8 w-8" />}
              title="Class Scheduling"
              description="Easy-to-use scheduling system for classes, personal training, and facility bookings."
              gradient="from-green-500 to-green-600"
            />
            <FeatureCard
              icon={<MessageSquare className="h-8 w-8" />}
              title="WhatsApp Integration"
              description="Automated notifications, reminders, and member communication via WhatsApp."
              gradient="from-purple-500 to-purple-600"
            />
            <FeatureCard
              icon={<TrendingUp className="h-8 w-8" />}
              title="Analytics & Reports"
              description="Comprehensive insights into your gym's performance and member engagement."
              gradient="from-orange-500 to-orange-600"
            />
            <FeatureCard
              icon={<Shield className="h-8 w-8" />}
              title="Multi-Tenant Security"
              description="Enterprise-grade security with complete data isolation between gym locations."
              gradient="from-red-500 to-red-600"
            />
            <FeatureCard
              icon={<Zap className="h-8 w-8" />}
              title="Lightning Fast"
              description="Built with modern technology for blazing-fast performance and reliability."
              gradient="from-yellow-500 to-yellow-600"
            />
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                Why Choose GymD Platform?
              </h2>
              <div className="space-y-6">
                <BenefitItem
                  icon={<CheckCircle className="h-6 w-6 text-green-500" />}
                  title="Multi-Location Support"
                  description="Manage multiple gym locations from a single platform with complete data separation."
                />
                <BenefitItem
                  icon={<CheckCircle className="h-6 w-6 text-green-500" />}
                  title="Automated Operations"
                  description="Reduce manual work with automated billing, notifications, and member communications."
                />
                <BenefitItem
                  icon={<CheckCircle className="h-6 w-6 text-green-500" />}
                  title="Member Engagement"
                  description="Keep members engaged with personalized communications and easy booking systems."
                />
                <BenefitItem
                  icon={<CheckCircle className="h-6 w-6 text-green-500" />}
                  title="Scalable Solution"
                  description="Grow from a single gym to a fitness empire with our scalable architecture."
                />
              </div>
            </div>
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8">
                <div className="flex items-center mb-6">
                  <Building2 className="h-8 w-8 text-blue-600 mr-3" />
                  <h3 className="text-2xl font-bold text-gray-900">Ready to Get Started?</h3>
                </div>
                <p className="text-gray-600 mb-6">
                  Join hundreds of gym owners who have transformed their business with GymD Platform.
                </p>
                <Button size="lg" variant="gradient" className="w-full">
                  Create Your Gym Account
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Ready to Transform Your Gym Business?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Start your free trial today and see how GymD Platform can help you manage your gym more efficiently.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="xl" variant="secondary">
              Start Free Trial
            </Button>
            <Button size="xl" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  gradient: string;
}

function FeatureCard({ icon, title, description, gradient }: FeatureCardProps) {
  return (
    <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
      <CardHeader>
        <div className={`bg-gradient-to-r ${gradient} text-white p-3 rounded-lg inline-block mb-4 group-hover:scale-110 transition-transform`}>
          {icon}
        </div>
        <CardTitle className="text-xl">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-gray-600">
          {description}
        </CardDescription>
      </CardContent>
    </Card>
  );
}

interface BenefitItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function BenefitItem({ icon, title, description }: BenefitItemProps) {
  return (
    <div className="flex items-start space-x-4">
      <div className="flex-shrink-0 mt-1">
        {icon}
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-1">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  );
}

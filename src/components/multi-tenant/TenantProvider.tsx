'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface TenantInfo {
  id: string;
  subdomain: string;
  name: string;
}

interface TenantContextType {
  tenant: TenantInfo | null;
  loading: boolean;
  error: string | null;
}

const TenantContext = createContext<TenantContextType>({
  tenant: null,
  loading: true,
  error: null,
});

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

interface TenantProviderProps {
  children: React.ReactNode;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ children }) => {
  const [tenant, setTenant] = useState<TenantInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTenantInfo = async () => {
      try {
        const response = await fetch('/api/tenant');

        if (!response.ok) {
          throw new Error(`Failed to fetch tenant information: ${response.status}`);
        }

        const text = await response.text();

        if (!text) {
          throw new Error('Empty response from tenant API');
        }

        const data = JSON.parse(text);
        setTenant(data.tenant);
      } catch (err) {
        console.error('Failed to fetch tenant info:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');

        // Fallback to default tenant on error
        setTenant({
          id: 'default',
          subdomain: 'default',
          name: 'GymD Platform',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTenantInfo();
  }, []);

  return (
    <TenantContext.Provider value={{ tenant, loading, error }}>
      {children}
    </TenantContext.Provider>
  );
};

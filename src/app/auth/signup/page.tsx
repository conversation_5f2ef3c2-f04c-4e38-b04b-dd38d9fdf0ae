'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTenant } from '@/components/multi-tenant/TenantProvider';
import { Building2, UserPlus, Mail } from 'lucide-react';
import Link from 'next/link';

export default function SignUpPage() {
  const { tenant, loading } = useTenant();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl">
              <UserPlus className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">
            Join {tenant?.name || 'GymD Platform'}
          </CardTitle>
          <CardDescription>
            {tenant?.subdomain !== 'default' 
              ? `Get access to ${tenant?.name}`
              : 'Create your gym account'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          {tenant?.subdomain !== 'default' ? (
            // For specific gym tenants
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <Building2 className="h-12 w-12 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Member Registration
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  To become a member of {tenant?.name}, please contact the gym directly or visit in person.
                </p>
              </div>
              
              <div className="space-y-3">
                <Button variant="gradient" className="w-full">
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Gym
                </Button>
                
                <Link href="/auth/signin">
                  <Button variant="outline" className="w-full">
                    Already have an account? Sign In
                  </Button>
                </Link>
              </div>
            </div>
          ) : (
            // For default tenant (gym owners)
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
                <Building2 className="h-12 w-12 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Start Your Gym Business
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  Create your gym account and start managing your fitness business with GymD Platform.
                </p>
              </div>
              
              <div className="space-y-3">
                <Button variant="gradient" className="w-full">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Gym Account
                </Button>
                
                <Link href="/auth/signin">
                  <Button variant="outline" className="w-full">
                    Already have an account? Sign In
                  </Button>
                </Link>
              </div>
              
              <div className="text-xs text-gray-500 space-y-1">
                <p>By creating an account, you agree to our</p>
                <div className="space-x-2">
                  <Button variant="link" className="p-0 h-auto text-xs">
                    Terms of Service
                  </Button>
                  <span>and</span>
                  <Button variant="link" className="p-0 h-auto text-xs">
                    Privacy Policy
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

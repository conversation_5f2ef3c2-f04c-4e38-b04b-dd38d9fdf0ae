'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, UserPlus, Mail } from 'lucide-react';
import Link from 'next/link';

export default function SignUpPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl">
              <UserPlus className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold">
            Join <PERSON>ymD Platform
          </CardTitle>
          <CardDescription>
            Create your gym account
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div className="space-y-4">
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
              <Building2 className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Gym Owner Registration
              </h3>
              <p className="text-gray-600 mb-4">
                Ready to digitize your gym? Join thousands of gym owners who trust GymD to manage their business.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                  Member Management
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                  Payment Processing
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                  Staff Management
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-2"></div>
                  Analytics & Reports
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <Button variant="gradient" className="w-full">
                <UserPlus className="mr-2 h-4 w-4" />
                Start Free Trial
              </Button>
              
              <Button variant="outline" className="w-full">
                <Mail className="mr-2 h-4 w-4" />
                Contact Sales
              </Button>
              
              <Link href="/auth/signin">
                <Button variant="ghost" className="w-full">
                  Already have an account? Sign In
                </Button>
              </Link>
            </div>
          </div>
          
          <div className="text-xs text-gray-500 space-y-1">
            <p>By creating an account, you agree to our</p>
            <div className="space-x-2">
              <Button variant="link" className="p-0 h-auto text-xs">
                Terms of Service
              </Button>
              <span>and</span>
              <Button variant="link" className="p-0 h-auto text-xs">
                Privacy Policy
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { TenantProvider, useTenant } from '@/components/multi-tenant/TenantProvider';
import { LandingPage } from '@/components/landing/LandingPage';
import { Loader2 } from 'lucide-react';

function HomePage() {
  const { tenant, loading, error } = useTenant();
  const router = useRouter();

  useEffect(() => {
    // If we have a resolved tenant (not default), redirect to login
    if (!loading && tenant && tenant.subdomain !== 'default') {
      router.push('/auth/signin');
    }
  }, [tenant, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading tenant information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️ Error</div>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // If tenant is resolved and not default, this will redirect to login
  // This return should not be reached due to the useEffect redirect
  if (tenant && tenant.subdomain !== 'default') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Show landing page for default tenant (no specific gym resolved)
  return <LandingPage />;

}

export default function Home() {
  return (
    <TenantProvider>
      <HomePage />
    </TenantProvider>
  );
}

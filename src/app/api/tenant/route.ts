import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import Gym from '@/models/Gym';

export async function GET(request: NextRequest) {
  try {
    // Get tenant information from headers (set by middleware)
    const tenantSubdomain = request.headers.get('x-tenant-subdomain') || 'default';

    // If it's the default tenant, return it directly
    if (tenantSubdomain === 'default') {
      return NextResponse.json({
        tenant: {
          id: 'default',
          subdomain: 'default',
          name: 'GymD Platform',
        },
        timestamp: new Date().toISOString(),
      });
    }

    // For specific gym subdomains, look up in database
    try {
      await connectToDatabase();

      const gym = await Gym.findOne({
        subdomain: tenantSubdomain,
        isActive: true
      }).select('_id name subdomain domain isActive').lean() as any;

      if (!gym) {
        // Return default tenant if gym not found
        return NextResponse.json({
          tenant: {
            id: 'default',
            subdomain: 'default',
            name: 'GymD Platform',
          },
          timestamp: new Date().toISOString(),
        });
      }

      return NextResponse.json({
        tenant: {
          id: gym._id.toString(),
          subdomain: gym.subdomain,
          name: gym.name,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (dbError) {
      console.error('Database error in tenant API:', dbError);

      // Fallback to default tenant on database error
      return NextResponse.json({
        tenant: {
          id: 'default',
          subdomain: 'default',
          name: 'GymD Platform',
        },
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('Tenant API error:', error);

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

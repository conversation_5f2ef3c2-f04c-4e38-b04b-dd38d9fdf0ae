import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User from '@/models/User';
import { passwordResetSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';

/**
 * POST /api/auth/reset-password - Reset password with token
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = passwordResetSchema.parse(body);
  
  // Find user by reset token
  const user = await User.findOne({
    passwordResetToken: validatedData.token,
    passwordResetExpires: { $gt: new Date() }
  }).select('+passwordResetToken +passwordResetExpires');
  
  if (!user) {
    return createErrorResponse('Invalid or expired reset token', 400);
  }
  
  // Update password and clear reset token
  user.password = validatedData.password;
  user.passwordResetToken = undefined;
  user.passwordResetExpires = undefined;
  
  await user.save();
  
  return createSuccessResponse(
    null, 
    'Password reset successfully'
  );
});

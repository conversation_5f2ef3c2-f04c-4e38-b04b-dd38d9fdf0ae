import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User from '@/models/User';
import Gym from '@/models/Gym';
import { userProfileUpdateSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';
import { requireAuth } from '@/lib/auth/middleware';
import { getCurrentUser } from '@/lib/auth/utils';

/**
 * GET /api/auth/profile - Get current user profile
 */
export const GET = requireAuth(withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return createErrorResponse('Authentication required', 401);
  }
  
  // Get user with gym information
  const user = await User.findById(currentUser.id)
    .select('-password -passwordResetToken -emailVerificationToken')
    .lean();
  
  if (!user) {
    return createErrorResponse('User not found', 404);
  }
  
  // Get gym information if user has gym association
  let gym = null;
  if (user.gymId) {
    gym = await Gym.findById(user.gymId)
      .select('name subdomain domain description address phone email')
      .lean();
  }
  
  // Get additional gyms if user has access to multiple
  let additionalGyms = [];
  if (user.gymIds && user.gymIds.length > 0) {
    additionalGyms = await Gym.find({
      _id: { $in: user.gymIds },
      _id: { $ne: user.gymId } // Exclude primary gym
    })
    .select('name subdomain domain description')
    .lean();
  }
  
  return createSuccessResponse({
    user,
    gym,
    additionalGyms
  }, 'Profile retrieved successfully');
}));

/**
 * PUT /api/auth/profile - Update current user profile
 */
export const PUT = requireAuth(withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return createErrorResponse('Authentication required', 401);
  }
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = userProfileUpdateSchema.parse(body);
  
  // Update user profile
  const updatedUser = await User.findByIdAndUpdate(
    currentUser.id,
    {
      ...validatedData,
      updatedAt: new Date(),
    },
    { 
      new: true, 
      runValidators: true,
      select: '-password -passwordResetToken -emailVerificationToken'
    }
  ).lean();
  
  if (!updatedUser) {
    return createErrorResponse('User not found', 404);
  }
  
  return createSuccessResponse(updatedUser, 'Profile updated successfully');
}));

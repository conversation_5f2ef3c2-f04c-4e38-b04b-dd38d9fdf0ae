import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User from '@/models/User';
import { passwordChangeSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';
import { requireAuth } from '@/lib/auth/middleware';
import { getCurrentUser } from '@/lib/auth/utils';

/**
 * POST /api/auth/change-password - Change user password
 * Requires authentication
 */
export const POST = requireAuth(withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return createErrorResponse('Authentication required', 401);
  }
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = passwordChangeSchema.parse(body);
  
  // Find user with password field
  const user = await User.findById(currentUser.id).select('+password');
  if (!user) {
    return createErrorResponse('User not found', 404);
  }
  
  // Verify current password
  const isCurrentPasswordValid = await user.comparePassword(validatedData.currentPassword);
  if (!isCurrentPasswordValid) {
    return createErrorResponse('Current password is incorrect', 400);
  }
  
  // Update password
  user.password = validatedData.newPassword;
  await user.save();
  
  return createSuccessResponse(
    null, 
    'Password changed successfully'
  );
}));

import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User, { UserStatus } from '@/models/User';
import { emailVerificationSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';

/**
 * POST /api/auth/verify-email - Verify email with token
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = emailVerificationSchema.parse(body);
  
  // Find user by verification token
  const user = await User.findOne({
    emailVerificationToken: validatedData.token
  }).select('+emailVerificationToken');
  
  if (!user) {
    return createErrorResponse('Invalid verification token', 400);
  }
  
  // Update user status and clear verification token
  user.emailVerified = new Date();
  user.emailVerificationToken = undefined;
  
  // Activate user if they were pending
  if (user.status === UserStatus.PENDING) {
    user.status = UserStatus.ACTIVE;
  }
  
  await user.save();
  
  return createSuccessResponse(
    null, 
    'Email verified successfully'
  );
});

/**
 * POST /api/auth/resend-verification - Resend verification email
 */
export const PUT = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const body = await request.json();
  const { email } = body;
  
  if (!email) {
    return createErrorResponse('Email is required', 400);
  }
  
  // Find user by email
  const user = await User.findOne({ 
    email: email.toLowerCase() 
  });
  
  if (!user) {
    return createErrorResponse('User not found', 404);
  }
  
  // Check if already verified
  if (user.emailVerified) {
    return createErrorResponse('Email is already verified', 400);
  }
  
  // Generate new verification token
  const verificationToken = user.generateEmailVerificationToken();
  await user.save();
  
  // TODO: Send verification email
  // await sendVerificationEmail(user.email, verificationToken);
  
  return createSuccessResponse(
    null, 
    'Verification email sent successfully'
  );
});

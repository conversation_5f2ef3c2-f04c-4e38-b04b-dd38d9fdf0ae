import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User, { UserRole, UserStatus } from '@/models/User';
import Gym from '@/models/Gym';
import { userRegistrationSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';

/**
 * POST /api/auth/register - Register new user
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = userRegistrationSchema.parse(body);
  
  // Check if user already exists
  const existingUser = await User.findOne({ 
    email: validatedData.email.toLowerCase() 
  });
  
  if (existingUser) {
    return createErrorResponse('User with this email already exists', 409);
  }
  
  // Validate gym association for non-super-admin users
  if (validatedData.role !== UserRole.SUPER_ADMIN && !validatedData.gymId) {
    return createErrorResponse('gymId is required for gym registration', 400);
  }
  
  // Verify gym exists if gymId is provided
  if (validatedData.gymId) {
    const gym = await Gym.findById(validatedData.gymId);
    if (!gym) {
      return createErrorResponse('Invalid gym ID', 400);
    }
  }
  
  // Prevent direct super admin registration (must be created by existing super admin)
  if (validatedData.role === UserRole.SUPER_ADMIN) {
    return createErrorResponse('Super admin accounts cannot be self-registered', 403);
  }
  
  // Create user
  const userData = {
    email: validatedData.email.toLowerCase(),
    password: validatedData.password,
    firstName: validatedData.firstName,
    lastName: validatedData.lastName,
    phone: validatedData.phone,
    role: validatedData.role || UserRole.MEMBER,
    gymId: validatedData.gymId,
    dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : undefined,
    gender: validatedData.gender,
    address: validatedData.address,
    status: UserStatus.PENDING // New registrations are pending by default
  };
  
  const user = new User(userData);
  
  // Generate email verification token
  const verificationToken = user.generateEmailVerificationToken();
  
  await user.save();
  
  // TODO: Send verification email
  // await sendVerificationEmail(user.email, verificationToken);
  
  // Remove sensitive data from response
  const userResponse = user.toObject();
  delete userResponse.password;
  delete userResponse.passwordResetToken;
  delete userResponse.emailVerificationToken;
  
  return createSuccessResponse(
    userResponse, 
    'User registered successfully. Please check your email for verification instructions.',
    null,
    201
  );
});

/**
 * GET /api/auth/register - Get registration information
 * Returns available gyms for registration
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  // Get available gyms for registration
  const gyms = await Gym.find({ isActive: true })
    .select('name subdomain description address')
    .sort({ name: 1 })
    .lean();
  
  return createSuccessResponse({
    gyms,
    availableRoles: [
      { value: UserRole.MEMBER, label: 'Member' },
      { value: UserRole.GYM_STAFF, label: 'Gym Staff' },
      { value: UserRole.GYM_ADMIN, label: 'Gym Administrator' },
      { value: UserRole.GYM_OWNER, label: 'Gym Owner' }
    ]
  }, 'Registration information retrieved successfully');
});

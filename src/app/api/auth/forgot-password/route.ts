import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User from '@/models/User';
import { passwordResetRequestSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';

/**
 * POST /api/auth/forgot-password - Request password reset
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = passwordResetRequestSchema.parse(body);
  
  // Find user by email
  const user = await User.findOne({ 
    email: validatedData.email.toLowerCase() 
  });
  
  // Always return success to prevent email enumeration
  if (!user) {
    return createSuccessResponse(
      null, 
      'If an account with that email exists, a password reset link has been sent.'
    );
  }
  
  // Generate password reset token
  const resetToken = user.generatePasswordResetToken();
  await user.save();
  
  // TODO: Send password reset email
  // await sendPasswordResetEmail(user.email, resetToken);
  
  return createSuccessResponse(
    null, 
    'If an account with that email exists, a password reset link has been sent.'
  );
});

import { NextRequest, NextResponse } from 'next/server';
import { TenantResolver } from '@/lib/tenant/resolver';

export async function GET(request: NextRequest) {
  try {
    const resolver = TenantResolver.getInstance();
    const tenant = await resolver.resolveTenant(request);
    
    const host = request.headers.get('host') || '';
    const subdomain = host.split('.')[0];
    
    return NextResponse.json({
      success: true,
      data: {
        host,
        subdomain,
        tenant,
        headers: {
          'x-tenant-subdomain': request.headers.get('x-tenant-subdomain'),
          'x-tenant-name': request.headers.get('x-tenant-name'),
        }
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

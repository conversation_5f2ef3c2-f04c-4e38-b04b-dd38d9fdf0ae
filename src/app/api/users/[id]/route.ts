import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User, { UserRole } from '@/models/User';
import { userAdminUpdateSchema, idParamSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';
import { requireAuth } from '@/lib/auth/middleware';
import { requireAdminPrivileges, getCurrentUser } from '@/lib/auth/utils';

interface RouteContext {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/users/[id] - Get user by ID
 */
export const GET = requireAuth(withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return createErrorResponse('Authentication required', 401);
  }
  
  // Users can view their own profile, admins can view any profile
  const isOwnProfile = currentUser.id === validatedParams.id;
  const isAdmin = [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN].includes(currentUser.role);
  
  if (!isOwnProfile && !isAdmin) {
    return createErrorResponse('Access denied', 403);
  }
  
  const user = await User.findById(validatedParams.id)
    .select('-password -passwordResetToken -emailVerificationToken')
    .lean();
  
  if (!user) {
    return createErrorResponse('User not found', 404);
  }
  
  // Additional gym access check for non-super-admin users
  if (!isOwnProfile && currentUser.role !== UserRole.SUPER_ADMIN) {
    const hasGymAccess = currentUser.gymId === user.gymId || 
                        (currentUser.gymIds && currentUser.gymIds.includes(user.gymId));
    
    if (!hasGymAccess) {
      return createErrorResponse('Access denied', 403);
    }
  }
  
  return createSuccessResponse(user, 'User retrieved successfully');
}));

/**
 * PUT /api/users/[id] - Update user by ID
 */
export const PUT = requireAuth(withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return createErrorResponse('Authentication required', 401);
  }
  
  const body = await request.json();
  
  // Check if user is updating their own profile or if they're an admin
  const isOwnProfile = currentUser.id === validatedParams.id;
  const isAdmin = [UserRole.SUPER_ADMIN, UserRole.GYM_OWNER, UserRole.GYM_ADMIN].includes(currentUser.role);
  
  if (!isOwnProfile && !isAdmin) {
    return createErrorResponse('Access denied', 403);
  }
  
  // Find the user to update
  const userToUpdate = await User.findById(validatedParams.id);
  if (!userToUpdate) {
    return createErrorResponse('User not found', 404);
  }
  
  // Additional gym access check for non-super-admin users
  if (!isOwnProfile && currentUser.role !== UserRole.SUPER_ADMIN) {
    const hasGymAccess = currentUser.gymId === userToUpdate.gymId || 
                        (currentUser.gymIds && currentUser.gymIds.includes(userToUpdate.gymId));
    
    if (!hasGymAccess) {
      return createErrorResponse('Access denied', 403);
    }
  }
  
  // Validate update data based on user permissions
  let validatedData;
  if (isAdmin && !isOwnProfile) {
    // Admins can update role, status, and gym associations
    validatedData = userAdminUpdateSchema.parse(body);
    
    // Prevent non-super-admins from creating super-admins
    if (validatedData.role === UserRole.SUPER_ADMIN && currentUser.role !== UserRole.SUPER_ADMIN) {
      return createErrorResponse('Cannot assign super admin role', 403);
    }
    
    // Prevent non-super-admins from modifying super-admins
    if (userToUpdate.role === UserRole.SUPER_ADMIN && currentUser.role !== UserRole.SUPER_ADMIN) {
      return createErrorResponse('Cannot modify super admin users', 403);
    }
  } else {
    // Regular users can only update their profile information
    const { role, status, gymId, gymIds, ...profileData } = userAdminUpdateSchema.parse(body);
    validatedData = profileData;
  }
  
  // Update user
  const updatedUser = await User.findByIdAndUpdate(
    validatedParams.id,
    {
      ...validatedData,
      updatedAt: new Date(),
    },
    { 
      new: true, 
      runValidators: true,
      select: '-password -passwordResetToken -emailVerificationToken'
    }
  ).lean();
  
  if (!updatedUser) {
    return createErrorResponse('User not found', 404);
  }
  
  return createSuccessResponse(updatedUser, 'User updated successfully');
}));

/**
 * DELETE /api/users/[id] - Delete user by ID
 * Requires admin privileges
 */
export const DELETE = requireAuth(withErrorHandling(async (request: NextRequest, context: RouteContext) => {
  await connectToDatabase();
  
  // Check admin privileges
  await requireAdminPrivileges();
  
  const { id } = await context.params;
  const validatedParams = idParamSchema.parse({ id });
  
  const currentUser = await getCurrentUser();
  if (!currentUser) {
    return createErrorResponse('Authentication required', 401);
  }
  
  // Find the user to delete
  const userToDelete = await User.findById(validatedParams.id);
  if (!userToDelete) {
    return createErrorResponse('User not found', 404);
  }
  
  // Prevent users from deleting themselves
  if (currentUser.id === validatedParams.id) {
    return createErrorResponse('Cannot delete your own account', 400);
  }
  
  // Prevent non-super-admins from deleting super-admins
  if (userToDelete.role === UserRole.SUPER_ADMIN && currentUser.role !== UserRole.SUPER_ADMIN) {
    return createErrorResponse('Cannot delete super admin users', 403);
  }
  
  // Additional gym access check for non-super-admin users
  if (currentUser.role !== UserRole.SUPER_ADMIN) {
    const hasGymAccess = currentUser.gymId === userToDelete.gymId || 
                        (currentUser.gymIds && currentUser.gymIds.includes(userToDelete.gymId));
    
    if (!hasGymAccess) {
      return createErrorResponse('Access denied', 403);
    }
  }
  
  // Delete user
  const deletedUser = await User.findByIdAndDelete(validatedParams.id)
    .select('-password -passwordResetToken -emailVerificationToken')
    .lean();
  
  if (!deletedUser) {
    return createErrorResponse('User not found', 404);
  }
  
  return createSuccessResponse(deletedUser, 'User deleted successfully');
}));

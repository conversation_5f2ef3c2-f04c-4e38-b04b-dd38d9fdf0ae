import { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/database/connection';
import User, { UserRole, UserStatus } from '@/models/User';
import { userRegistrationSchema, paginationSchema } from '@/lib/validations/schemas';
import { 
  createSuccessResponse, 
  createErrorResponse,
  withErrorHandling
} from '@/lib/utils/api';
import { requireAuth } from '@/lib/auth/middleware';
import { requireAdminPrivileges } from '@/lib/auth/utils';

/**
 * GET /api/users - List users with pagination and filtering
 * Requires admin privileges
 */
export const GET = requireAuth(withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  // Check admin privileges
  await requireAdminPrivileges();
  
  const { searchParams } = new URL(request.url);
  const { page, limit, search, sortBy, sortOrder } = paginationSchema.parse({
    page: searchParams.get('page') || '1',
    limit: searchParams.get('limit') || '10',
    search: searchParams.get('search'),
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: searchParams.get('sortOrder') || 'desc'
  });
  
  // Get filter parameters
  const role = searchParams.get('role') as UserRole;
  const status = searchParams.get('status') as UserStatus;
  const gymId = searchParams.get('gymId');
  
  // Build query
  const query: any = {};
  
  // Search functionality
  if (search) {
    query.$or = [
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }
  
  // Filter by role
  if (role && Object.values(UserRole).includes(role)) {
    query.role = role;
  }
  
  // Filter by status
  if (status && Object.values(UserStatus).includes(status)) {
    query.status = status;
  }
  
  // Filter by gym
  if (gymId) {
    query.$or = [
      { gymId: gymId },
      { gymIds: { $in: [gymId] } }
    ];
  }
  
  // Calculate pagination
  const skip = (page - 1) * limit;
  
  // Build sort object
  const sort: any = {};
  sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
  
  // Execute queries
  const [users, total] = await Promise.all([
    User.find(query)
      .select('-password -passwordResetToken -emailVerificationToken')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean(),
    User.countDocuments(query)
  ]);
  
  const totalPages = Math.ceil(total / limit);
  
  return createSuccessResponse(users, 'Users retrieved successfully', {
    pagination: {
      page,
      limit,
      total,
      totalPages
    }
  });
}));

/**
 * POST /api/users - Create new user
 * Requires admin privileges
 */
export const POST = requireAuth(withErrorHandling(async (request: NextRequest) => {
  await connectToDatabase();
  
  // Check admin privileges
  await requireAdminPrivileges();
  
  const body = await request.json();
  
  // Validate request body
  const validatedData = userRegistrationSchema.parse(body);
  
  // Check if user already exists
  const existingUser = await User.findOne({ 
    email: validatedData.email.toLowerCase() 
  });
  
  if (existingUser) {
    return createErrorResponse('User with this email already exists', 409);
  }
  
  // Validate gym association for non-super-admin users
  if (validatedData.role !== UserRole.SUPER_ADMIN && !validatedData.gymId) {
    return createErrorResponse('gymId is required for non-super-admin users', 400);
  }
  
  // Create user
  const userData = {
    email: validatedData.email.toLowerCase(),
    password: validatedData.password,
    firstName: validatedData.firstName,
    lastName: validatedData.lastName,
    phone: validatedData.phone,
    role: validatedData.role,
    gymId: validatedData.gymId,
    dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : undefined,
    gender: validatedData.gender,
    address: validatedData.address,
    status: UserStatus.ACTIVE // Admin-created users are active by default
  };
  
  const user = new User(userData);
  await user.save();
  
  // Remove sensitive data from response
  const userResponse = user.toObject();
  delete userResponse.password;
  delete userResponse.passwordResetToken;
  delete userResponse.emailVerificationToken;
  
  return createSuccessResponse(userResponse, 'User created successfully', null, 201);
}));
